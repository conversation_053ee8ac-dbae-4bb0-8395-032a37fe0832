<template>
  <div class="settings-panel">
    <MySettings />
  </div>
</template>

<script setup>
import MySettings from '@/components/my/MySettings.vue';
</script>

<style scoped>
/* 设置面板样式 */
.settings-panel {
  flex: 1;
  overflow: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(150, 150, 150, 0.3) transparent;
}

.settings-panel::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.settings-panel::-webkit-scrollbar-track {
  background: transparent;
}

.settings-panel::-webkit-scrollbar-thumb {
  background: rgba(150, 150, 150, 0.3);
  border-radius: 4px;
}

.settings-panel::-webkit-scrollbar-thumb:hover {
  background: rgba(150, 150, 150, 0.5);
}
</style>
