<template>
    <div class="my-page">
      <div class="container py-3 full-width">
        <div class="page-header">
          <router-link to="/" class="back-to-home-btn">
            <i class="back-icon"></i>
            <span>返回首页</span>
          </router-link>
          <h1 class="page-title">AI智能OBE智慧教学平台</h1>
          <div class="header-subtitle">学生学习中心</div>
        </div>
        
        <!-- 左侧菜单和主内容区 -->
        <div class="content-layout">
          <!-- 左侧菜单 -->
          <div class="sidebar">
            <div class="sidebar-menu">
              <div 
                class="menu-item" 
                :class="{ active: activeTab === 'courses' }" 
                @click="activeTab = 'courses'"
              >
                <i class="menu-icon courses-icon"></i>
                <span class="menu-text">我的课程</span>
              </div>
              <div 
                class="menu-item" 
                :class="{ active: activeTab === 'knowledge-graph' }" 
                @click="activeTab = 'knowledge-graph'"
              >
                <i class="menu-icon knowledge-graph-icon"></i>
                <span class="menu-text">知识图谱</span>
              </div>
              <div 
                class="menu-item" 
                :class="{ active: activeTab === 'homework' }" 
                @click="activeTab = 'homework'"
              >
                <i class="menu-icon homework-icon"></i>
                <span class="menu-text">作业</span>
              </div>
              <div 
                class="menu-item" 
                :class="{ active: activeTab === 'discussion' }" 
                @click="activeTab = 'discussion'"
              >
                <i class="menu-icon discussion-icon"></i>
                <span class="menu-text">讨论</span>
              </div>
              <div 
                class="menu-item" 
                :class="{ active: activeTab === 'group' }" 
                @click="activeTab = 'group'"
              >
                <i class="menu-icon group-icon"></i>
                <span class="menu-text">小组</span>
              </div>
              <div 
                class="menu-item" 
                :class="{ active: activeTab === 'ai-assistant' }" 
                @click="activeTab = 'ai-assistant'"
              >
                <i class="menu-icon ai-assistant-icon"></i>
                <span class="menu-text">AI助手</span>
              </div>
              <div 
                class="menu-item" 
                :class="{ active: activeTab === 'materials' }" 
                @click="activeTab = 'materials'"
              >
                <i class="menu-icon materials-icon"></i>
                <span class="menu-text">学习资料</span>
              </div>
              <div 
                class="menu-item" 
                :class="{ active: activeTab === 'settings' }" 
                @click="activeTab = 'settings'"
              >
                <i class="menu-icon settings-icon"></i>
                <span class="menu-text">设置</span>
              </div>
            </div>
          </div>
          
          <!-- 主内容区域 -->
          <div class="main-content">
            <!-- 我的课程模块 -->
            <StudentCourses v-if="activeTab === 'courses'" />

            <!-- 知识图谱模块 -->
            <StudentKnowledgeGraph v-if="activeTab === 'knowledge-graph'" />

            <!-- 作业模块 -->
            <StudentHomework v-if="activeTab === 'homework'" />

            <!-- 讨论模块 -->
            <StudentDiscussion v-if="activeTab === 'discussion'" />

            <!-- 小组模块 -->
            <StudentGroup v-if="activeTab === 'group'" />

            <!-- AI助手模块 -->
            <StudentAIAssistant v-if="activeTab === 'ai-assistant'" />

            <!-- 学习资料模块 -->
            <StudentMaterials v-if="activeTab === 'materials'" />

            <!-- 设置模块 -->
            <StudentSettings v-if="activeTab === 'settings'" />
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref, onMounted } from 'vue';
  import { getCurrentUser } from '@/api/auth';
  import { useRoute } from 'vue-router';

  // 导入拆分的组件
  import StudentCourses from '@/components/student/modules/StudentCourses.vue';
  import StudentKnowledgeGraph from '@/components/student/modules/StudentKnowledgeGraph.vue';
  import StudentHomework from '@/components/student/modules/StudentHomework.vue';
  import StudentDiscussion from '@/components/student/modules/StudentDiscussion.vue';
  import StudentGroup from '@/components/student/modules/StudentGroup.vue';
  import StudentAIAssistant from '@/components/student/modules/StudentAIAssistant.vue';
  import StudentMaterials from '@/components/student/modules/StudentMaterials.vue';
  import StudentSettings from '@/components/student/modules/StudentSettings.vue';

  const route = useRoute();

  // 当前用户信息
  const currentUser = ref(getCurrentUser() || {});

  // 当前激活的标签页 - 默认显示课程
  const activeTab = ref('courses');
  
  // 组件挂载时加载用户数据
  onMounted(async () => {
    // TODO: 从API获取最新的用户数据
    console.log('加载学生用户数据');

    // 检查URL参数中是否指定了要显示的标签页
    if (route.query.tab) {
      activeTab.value = route.query.tab;
    }
  });








  </script>

  <style scoped>
  /* 基础页面样式 */
  .my-page {
    min-height: 100vh;
    background-color: var(--background-color-secondary);
    margin: 0;
    padding: 0;
  }

  .full-width {
    max-width: 100% !important;
    padding-left: 0;
    padding-right: 0;
    margin: 0;
    width: 100%;
  }

  .page-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    padding: 0.25rem 0.5rem;
  }

  .back-to-home-btn {
    display: flex;
    align-items: center;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    margin-right: 1rem;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-md);
    transition: all var(--transition-normal);
  }

  .back-to-home-btn:hover {
    background-color: var(--hover-color);
  }

  .back-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 0.5rem;
    position: relative;
  }

  .back-icon::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 12px;
    height: 12px;
    border-left: 2px solid var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
    transform: translateY(-50%) rotate(45deg);
  }

  .page-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
  }

  .header-subtitle {
    font-size: 0.9375rem;
    color: var(--text-color-secondary);
    margin-top: 0.25rem;
  }

  /* 布局样式 */
  .content-layout {
    display: flex;
    gap: 0;
    min-height: 600px;
    height: 100vh;
    width: 100%;
    margin: 0;
    padding: 0;
  }

  .sidebar {
    width: 180px;
    flex-shrink: 0;
    background-color: #f5f5f5;
    border-radius: 0;
    box-shadow: none;
    padding: 0.5rem 0;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(150, 150, 150, 0.3) transparent;
  }

  .sidebar-menu {
    display: flex;
    flex-direction: column;
  }

  .menu-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: var(--text-color);
    cursor: pointer;
    transition: all 0.2s ease;
    border-left: none;
    margin-bottom: 4px;
    border-radius: 0;
  }

  .menu-item:hover {
    background-color: #e9e9e9;
  }

  .menu-item.active {
    background-color: #e9e9e9;
    color: var(--primary-color);
    font-weight: 500;
    border-left: 4px solid var(--primary-color);
  }

  .menu-icon {
    width: 20px;
    height: 20px;
    margin-right: 0.75rem;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0.7;
  }

  .menu-item.active .menu-icon {
    opacity: 1;
  }

  .menu-text {
    font-size: 0.9375rem;
    font-weight: 400;
  }

  .main-content {
    flex: 1;
    background-color: #ffffff;
    border-radius: 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    padding: 1rem;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    width: calc(100% - 180px);
  }



  /* 菜单图标样式 */
  .courses-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M12 3L1 9l11 6 9-4.91V17h2V9L12 3zm0 11.13L6.08 12 12 9.87 17.92 12 12 14.13z'/%3E%3C/svg%3E");
  }

  .knowledge-graph-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M19.5 5.5v13h-15v-13h15m0-2h-15c-1.1 0-2 .9-2 2v13c0 1.1.9 2 2 2h15c1.1 0 2-.9 2-2v-13c0-1.1-.9-2-2-2zM9 8h2v8H9zm4 3h2v5h-2zm-8 0h2v5H5z'/%3E%3C/svg%3E");
  }

  .homework-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M14 2H6c-1.1 0-2 .9-2 2v16c0 1.1.89 2 2 2h12c1.11 0 2-.9 2-2V8l-6-6zm4 18H6V4h7v5h5v11z'/%3E%3C/svg%3E");
  }

  .discussion-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4v5l7-5h5c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 14H11l-4 3v-3H4V4h16v12z'/%3E%3C/svg%3E");
  }

  .group-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A1.998 1.998 0 0 0 18 7h-2c-.8 0-1.54.37-2.01 1.01l-.74 2.22-2.99-2.99C10.05 6.85 9.49 6.67 8.9 6.67c-1.66 0-3 1.34-3 3 0 .59.18 1.15.5 1.61L9 14.89V22h2v-6h2v6h4z'/%3E%3C/svg%3E");
  }

  .ai-assistant-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M20 9V7c0-1.1-.9-2-2-2h-3c0-1.66-1.34-3-3-3S9 3.34 9 5H6c-1.1 0-2 .9-2 2v2c-1.66 0-3 1.34-3 3s1.34 3 3 3v4c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2v-4c1.66 0 3-1.34 3-3s-1.34-3-3-3zm-2 10H6V7h12v12z'/%3E%3C/svg%3E");
  }

  .materials-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z'/%3E%3C/svg%3E");
  }

  .settings-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6z'/%3E%3C/svg%3E");
  }



  /* 响应式设计 */
  @media (max-width: 768px) {
    .content-layout {
      flex-direction: column;
    }

    .sidebar {
      width: 100%;
      height: auto;
      order: 2;
    }

    .main-content {
      order: 1;
    }
  }
  </style>
