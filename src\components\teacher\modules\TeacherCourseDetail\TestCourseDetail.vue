<template>
  <div class="test-course-detail">
    <h1>测试课程详情页面</h1>
    <p>课程ID: {{ $route.params.courseId }}</p>
    <button @click="goBack">返回课程列表</button>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';

const router = useRouter();

const goBack = () => {
  router.push('/teacher/my');
};
</script>

<style scoped>
.test-course-detail {
  padding: 2rem;
  text-align: center;
}

button {
  padding: 0.5rem 1rem;
  background-color: #6366f1;
  color: white;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  margin-top: 1rem;
}

button:hover {
  background-color: #4f46e5;
}
</style>
