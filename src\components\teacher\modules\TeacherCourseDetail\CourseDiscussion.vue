<template>
  <div class="course-discussion">
    <!-- 页面标题 -->
    <div class="section-header">
      <h2 class="section-title">讨论区</h2>
    </div>

    <!-- 讨论统计 -->
    <div class="discussion-stats">
      <div class="stat-card">
        <div class="stat-number">{{ discussionStats.totalTopics }}</div>
        <div class="stat-label">讨论话题</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ discussionStats.totalReplies }}</div>
        <div class="stat-label">回复数量</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ discussionStats.activeUsers }}</div>
        <div class="stat-label">活跃用户</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ discussionStats.todayPosts }}</div>
        <div class="stat-label">今日发帖</div>
      </div>
    </div>

    <!-- 会话列表部分 -->
    <div class="session-section">
      <div class="section-header">
        <h3 class="section-title">讨论会话</h3>
        <div class="section-actions">
          <button class="btn btn-primary btn-sm" @click="createNewSession">
            <i class="btn-icon plus-icon"></i>
            新建会话
          </button>
          <button class="btn btn-secondary btn-sm" @click="refreshSessionList">
            <i class="btn-icon refresh-icon"></i>
            刷新
          </button>
        </div>
      </div>

      <!-- 会话列表 -->
      <div v-if="sessionLoading" class="loading-state">
        <div class="loading-spinner"></div>
        <p>正在加载会话列表...</p>
      </div>

      <div v-else-if="sessionList.length > 0" class="session-list">
        <div
          v-for="session in sessionList"
          :key="session.id"
          class="session-item"
          @click="viewSession(session)"
        >
          <div class="session-info">
            <h4 class="session-name">{{ session.name || `会话 #${session.id}` }}</h4>
            <p class="session-remark" v-if="session.remark">{{ session.remark }}</p>
            <div class="session-meta">
              <span class="session-time">创建时间: {{ formatTime(session.createTime) }}</span>
              <span class="session-creator" v-if="session.createBy">创建者: {{ session.createBy }}</span>
            </div>
          </div>
          <div class="session-actions">
            <button class="btn btn-text btn-sm" @click.stop="editSession(session)">
              编辑
            </button>
            <button class="btn btn-text btn-sm text-danger" @click.stop="deleteSessionItem(session)">
              删除
            </button>
          </div>
        </div>
      </div>

      <div v-else class="empty-state">
        <div class="empty-icon">💬</div>
        <h3 class="empty-title">暂无会话</h3>
        <p class="empty-description">创建您的第一个讨论会话</p>
        <button class="btn btn-primary" @click="createNewSession">
          <i class="plus-icon"></i>
          新建会话
        </button>
      </div>
    </div>

    <!-- 创建会话模态框 -->
    <div v-if="showCreateSessionModal" class="modal-overlay" @click="closeCreateSessionModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3 class="modal-title">创建新的讨论会话</h3>
          <button class="modal-close" @click="closeCreateSessionModal">×</button>
        </div>

        <div class="modal-body">
          <form @submit.prevent="submitCreateSession">
            <!-- 会话名称 -->
            <div class="form-group">
              <label for="sessionName" class="form-label">会话名称 <span class="required">*</span></label>
              <input
                id="sessionName"
                v-model="newSessionForm.name"
                type="text"
                class="form-input"
                placeholder="请输入会话名称"
                required
                maxlength="50"
              />
            </div>

            <!-- 会话备注 -->
            <div class="form-group">
              <label for="sessionRemark" class="form-label">会话备注</label>
              <textarea
                id="sessionRemark"
                v-model="newSessionForm.remark"
                class="form-textarea"
                placeholder="请输入会话备注（可选）"
                rows="3"
                maxlength="200"
              ></textarea>
            </div>

            <!-- 选择参与成员 -->
            <div class="form-group">
              <label class="form-label">选择参与成员</label>
              <div class="member-selection">
                <!-- 搜索框 -->
                <div class="search-box">
                  <input
                    v-model="memberSearchKeyword"
                    type="text"
                    class="form-input"
                    placeholder="搜索学生姓名..."
                    @input="searchMembers"
                  />
                </div>

                <!-- 成员列表 -->
                <div class="member-list" v-if="availableMembers.length > 0">
                  <div class="member-list-header">
                    <label class="checkbox-label">
                      <input
                        type="checkbox"
                        :checked="isAllMembersSelected"
                        @change="toggleAllMembers"
                      />
                      全选 ({{ availableMembers.length }}人)
                    </label>
                  </div>

                  <div class="member-items">
                    <label
                      v-for="member in availableMembers"
                      :key="member.userId"
                      class="member-item"
                    >
                      <input
                        type="checkbox"
                        :value="member.userId"
                        v-model="newSessionForm.memberIds"
                      />
                      <div class="member-info">
                        <span class="member-name">{{ member.nickName || member.userName }}</span>
                        <span class="member-detail">{{ member.userName }}</span>
                      </div>
                    </label>
                  </div>
                </div>

                <!-- 加载状态 -->
                <div v-else-if="membersLoading" class="loading-members">
                  <div class="loading-spinner-small"></div>
                  <span>正在加载学生列表...</span>
                </div>

                <!-- 无成员状态 -->
                <div v-else class="no-members">
                  <p>暂无可选择的学生</p>
                </div>

                <!-- 已选择的成员数量 -->
                <div v-if="newSessionForm.memberIds.length > 0" class="selected-count">
                  已选择 {{ newSessionForm.memberIds.length }} 名学生
                </div>
              </div>
            </div>
          </form>
        </div>

        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" @click="closeCreateSessionModal">
            取消
          </button>
          <button
            type="button"
            class="btn btn-primary"
            @click="submitCreateSession"
            :disabled="createSessionLoading || !newSessionForm.name.trim()"
          >
            <span v-if="createSessionLoading">创建中...</span>
            <span v-else>创建会话</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 编辑会话模态框 -->
    <div v-if="showEditSessionModal" class="modal-overlay" @click="closeEditSessionModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3 class="modal-title">编辑讨论会话</h3>
          <button class="modal-close" @click="closeEditSessionModal">×</button>
        </div>

        <div class="modal-body">
          <form @submit.prevent="submitEditSession">
            <!-- 会话名称 -->
            <div class="form-group">
              <label for="editSessionName" class="form-label">会话名称 <span class="required">*</span></label>
              <input
                id="editSessionName"
                v-model="editSessionForm.name"
                type="text"
                class="form-input"
                placeholder="请输入会话名称"
                required
                maxlength="50"
              />
            </div>

            <!-- 会话备注 -->
            <div class="form-group">
              <label for="editSessionRemark" class="form-label">会话备注</label>
              <textarea
                id="editSessionRemark"
                v-model="editSessionForm.remark"
                class="form-textarea"
                placeholder="请输入会话备注（可选）"
                rows="3"
                maxlength="200"
              ></textarea>
            </div>

            <!-- 会话信息 -->
            <div class="form-group">
              <label class="form-label">会话信息</label>
              <div class="session-info-display">
                <div class="info-item">
                  <span class="info-label">会话ID:</span>
                  <span class="info-value">{{ editSessionForm.id }}</span>
                </div>
                <div class="info-item" v-if="editSessionForm.createBy">
                  <span class="info-label">创建者:</span>
                  <span class="info-value">{{ editSessionForm.createBy }}</span>
                </div>
                <div class="info-item" v-if="editSessionForm.createTime">
                  <span class="info-label">创建时间:</span>
                  <span class="info-value">{{ formatTime(editSessionForm.createTime) }}</span>
                </div>
              </div>
            </div>
          </form>
        </div>

        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" @click="closeEditSessionModal">
            取消
          </button>
          <button
            type="button"
            class="btn btn-primary"
            @click="submitEditSession"
            :disabled="editSessionLoading || !editSessionForm.name.trim()"
          >
            <span v-if="editSessionLoading">保存中...</span>
            <span v-else>保存修改</span>
          </button>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { getSessionList, getSessionListByCourse, createSession, updateSession, deleteSession } from '@/api/session';
import { getUserListByDept, getStudentListByCourse, searchUsers } from '@/api/users';

// 定义props
const props = defineProps({
  courseId: {
    type: [String, Number],
    required: true
  }
});

// 定义emits
const emit = defineEmits(['refresh']);

// 响应式数据
const sessionList = ref([]);
const sessionLoading = ref(false);

// 讨论统计
const discussionStats = ref({
  totalTopics: 0,
  totalReplies: 0,
  activeUsers: 0,
  todayPosts: 0
});

// 创建会话相关数据
const showCreateSessionModal = ref(false);
const createSessionLoading = ref(false);
const newSessionForm = ref({
  name: '',
  remark: '',
  memberIds: []
});

// 成员选择相关数据
const availableMembers = ref([]);
const membersLoading = ref(false);
const memberSearchKeyword = ref('');

// 编辑会话相关数据
const showEditSessionModal = ref(false);
const editSessionLoading = ref(false);
const editSessionForm = ref({
  id: 0,
  name: '',
  remark: '',
  createBy: '',
  createTime: '',
  updateBy: '',
  updateTime: '',
  params: {}
});

// 计算属性
const isAllMembersSelected = computed(() => {
  return availableMembers.value.length > 0 &&
         newSessionForm.value.memberIds.length === availableMembers.value.length;
});



// 加载会话列表
const loadSessionList = async () => {
  sessionLoading.value = true;
  try {
    console.log('正在加载课程会话列表，课程ID:', props.courseId);

    // 调用会话列表API
    const response = await getSessionListByCourse(props.courseId);
    console.log('会话列表API响应:', response);

    if (response && (response.code === 0 || response.code === 200)) {
      sessionList.value = response.rows || [];
      console.log('会话列表加载成功，数量:', sessionList.value.length);

      // 更新讨论统计中的会话相关数据
      discussionStats.value.totalTopics = sessionList.value.length;
    } else {
      console.warn('会话列表API返回异常:', response);
      sessionList.value = [];
    }
  } catch (error) {
    console.error('加载会话列表失败:', error);
    sessionList.value = [];
    // 可以在这里添加错误提示
  } finally {
    sessionLoading.value = false;
  }
};



// 格式化时间
const formatTime = (timeString) => {
  if (!timeString) return '未知';

  try {
    // 处理 "2025-07-28 10:41:30" 格式的时间字符串
    const date = new Date(timeString.replace(' ', 'T')); // 转换为ISO格式
    const now = new Date();
    const diffMs = now - date;
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMs < 0) {
      // 如果是未来时间，直接显示日期
      return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    } else if (diffHours < 1) {
      return '刚刚';
    } else if (diffHours < 24) {
      return `${diffHours}小时前`;
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    }
  } catch (error) {
    console.error('时间格式化失败:', error, timeString);
    return timeString; // 如果格式化失败，返回原始字符串
  }
};



// 会话相关操作函数

// 创建新会话 - 打开模态框
const createNewSession = async () => {
  console.log('打开创建会话模态框');

  // 重置表单
  newSessionForm.value = {
    name: '',
    remark: '',
    memberIds: []
  };

  // 显示模态框
  showCreateSessionModal.value = true;

  // 加载可选成员列表
  await loadAvailableMembers();
};

// 关闭创建会话模态框
const closeCreateSessionModal = () => {
  showCreateSessionModal.value = false;
  newSessionForm.value = {
    name: '',
    remark: '',
    memberIds: []
  };
  memberSearchKeyword.value = '';
};

// 加载可选成员列表
const loadAvailableMembers = async () => {
  membersLoading.value = true;
  try {
    console.log('正在加载课程学生列表，课程ID:', props.courseId);

    // 尝试通过课程ID获取学生列表
    let response = await getStudentListByCourse(props.courseId);

    // 如果课程学生接口不存在或失败，尝试其他方式
    if (!response || response.code !== 0) {
      console.warn('课程学生接口不可用，尝试获取所有用户列表');
      response = await searchUsers({ pageSize: 100 });
    }

    if (response && (response.code === 0 || response.code === 200)) {
      availableMembers.value = response.rows || [];
      console.log('加载成员列表成功，数量:', availableMembers.value.length);
    } else {
      console.warn('加载成员列表失败:', response);
      availableMembers.value = [];
    }
  } catch (error) {
    console.error('加载成员列表异常:', error);
    availableMembers.value = [];
  } finally {
    membersLoading.value = false;
  }
};

// 搜索成员
const searchMembers = async () => {
  if (!memberSearchKeyword.value.trim()) {
    // 如果搜索关键词为空，重新加载所有成员
    await loadAvailableMembers();
    return;
  }

  membersLoading.value = true;
  try {
    const response = await searchUsers({
      keyword: memberSearchKeyword.value.trim(),
      pageSize: 100
    });

    if (response && (response.code === 0 || response.code === 200)) {
      availableMembers.value = response.rows || [];
      console.log('搜索成员成功，数量:', availableMembers.value.length);
    } else {
      console.warn('搜索成员失败:', response);
      availableMembers.value = [];
    }
  } catch (error) {
    console.error('搜索成员异常:', error);
    availableMembers.value = [];
  } finally {
    membersLoading.value = false;
  }
};

// 全选/取消全选成员
const toggleAllMembers = (event) => {
  if (event.target.checked) {
    // 全选
    newSessionForm.value.memberIds = availableMembers.value.map(member => member.userId);
  } else {
    // 取消全选
    newSessionForm.value.memberIds = [];
  }
};

// 提交创建会话
const submitCreateSession = async () => {
  if (!newSessionForm.value.name.trim()) {
    alert('请输入会话名称');
    return;
  }

  createSessionLoading.value = true;
  try {
    console.log('正在创建会话...', newSessionForm.value);

    const sessionData = {
      name: newSessionForm.value.name.trim(),
      remark: newSessionForm.value.remark.trim(),
      memberIds: newSessionForm.value.memberIds,
      courseId: props.courseId
    };

    const response = await createSession(sessionData);
    if (response && (response.code === 0 || response.code === 200 || response.success)) {
      console.log('创建会话成功');

      // 关闭模态框
      closeCreateSessionModal();

      // 重新加载会话列表
      await loadSessionList();

      // 显示成功提示
      alert('创建会话成功！');
    } else {
      console.error('创建会话失败:', response);
      alert(response.msg || '创建会话失败，请重试');
    }
  } catch (error) {
    console.error('创建会话异常:', error);
    alert('创建会话失败，请重试');
  } finally {
    createSessionLoading.value = false;
  }
};

// 刷新会话列表
const refreshSessionList = async () => {
  console.log('刷新会话列表');
  await loadSessionList();
};

// 查看会话
const viewSession = (session) => {
  console.log('查看会话:', session);
  // TODO: 实现查看会话详情逻辑，可能跳转到会话详情页面
};

// 编辑会话 - 打开编辑模态框
const editSession = (session) => {
  console.log('编辑会话:', session);

  // 填充编辑表单
  editSessionForm.value = {
    id: session.id,
    name: session.name || '',
    remark: session.remark || '',
    createBy: session.createBy || '',
    createTime: session.createTime || '',
    updateBy: session.updateBy || '',
    updateTime: session.updateTime || '',
    params: session.params || {}
  };

  // 显示编辑模态框
  showEditSessionModal.value = true;
};

// 关闭编辑会话模态框
const closeEditSessionModal = () => {
  showEditSessionModal.value = false;
  editSessionForm.value = {
    id: 0,
    name: '',
    remark: '',
    createBy: '',
    createTime: '',
    updateBy: '',
    updateTime: '',
    params: {}
  };
};

// 提交编辑会话
const submitEditSession = async () => {
  if (!editSessionForm.value.name.trim()) {
    alert('请输入会话名称');
    return;
  }

  if (!editSessionForm.value.id) {
    alert('会话ID不能为空');
    return;
  }

  editSessionLoading.value = true;
  try {
    console.log('正在更新会话...', editSessionForm.value);

    const sessionData = {
      id: editSessionForm.value.id,
      name: editSessionForm.value.name.trim(),
      remark: editSessionForm.value.remark.trim(),
      createBy: editSessionForm.value.createBy,
      createTime: editSessionForm.value.createTime,
      updateBy: editSessionForm.value.updateBy,
      updateTime: editSessionForm.value.updateTime,
      params: editSessionForm.value.params
    };

    const response = await updateSession(sessionData);
    if (response && (response.code === 0 || response.code === 200 || response.success)) {
      console.log('更新会话成功');

      // 关闭模态框
      closeEditSessionModal();

      // 重新加载会话列表
      await loadSessionList();

      // 显示成功提示
      alert('更新会话成功！');
    } else {
      console.error('更新会话失败:', response);
      alert(response.msg || '更新会话失败，请重试');
    }
  } catch (error) {
    console.error('更新会话异常:', error);
    alert('更新会话失败，请重试');
  } finally {
    editSessionLoading.value = false;
  }
};

// 删除会话
const deleteSessionItem = async (session) => {
  console.log('删除会话:', session);
  if (!confirm(`确定要删除会话"${session.name || session.id}"吗？`)) {
    return;
  }

  try {
    const response = await deleteSession(session.id);
    if (response && (response.code === 0 || response.code === 200)) {
      console.log('删除会话成功');
      await loadSessionList(); // 重新加载会话列表
    } else {
      console.error('删除会话失败:', response);
      alert('删除会话失败，请重试');
    }
  } catch (error) {
    console.error('删除会话异常:', error);
    alert('删除会话失败，请重试');
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadSessionList();
});
</script>

<style scoped>
/* 讨论区样式 */
.course-discussion {
  background-color: var(--background-color, #ffffff);
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  background-color: var(--background-color, #ffffff);
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color, #1f2937);
  margin: 0;
}

.section-actions {
  display: flex;
  gap: 0.75rem;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.btn-primary {
  background-color: var(--primary-color, #6366f1);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-color-dark, #4f46e5);
}

.btn-secondary {
  background-color: var(--background-color-secondary, #f3f4f6);
  color: var(--text-color, #374151);
  border-color: var(--border-color, #d1d5db);
}

.btn-secondary:hover {
  background-color: var(--background-color-tertiary, #e5e7eb);
}

/* 讨论统计 */
.discussion-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  padding: 1.5rem;
  background-color: var(--background-color-secondary, #f9fafb);
  border-bottom: 1px solid var(--border-color, #e5e7eb);
}

.stat-card {
  text-align: center;
  padding: 1rem;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color, #6366f1);
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-color-secondary, #6b7280);
}



/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--text-color-secondary, #6b7280);
}

.loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 2px solid var(--border-color, #e5e7eb);
  border-top: 2px solid var(--primary-color, #6366f1);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}





/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.empty-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-color, #1f2937);
  margin: 0 0 0.5rem 0;
}

.empty-description {
  color: var(--text-color-secondary, #6b7280);
  margin: 0 0 1.5rem 0;
}

/* 图标样式 */
.btn-icon, .plus-icon, .manage-icon, .reply-icon, .view-icon, .time-icon,
.pin-icon, .edit-icon, .delete-icon {
  width: 1rem;
  height: 1rem;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

.plus-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

.manage-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

.reply-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M7.707 3.293a1 1 0 010 1.414L5.414 7H11a7 7 0 017 7v2a1 1 0 11-2 0v-2a5 5 0 00-5-5H5.414l2.293 2.293a1 1 0 11-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

.view-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath d='M10 12a2 2 0 100-4 2 2 0 000 4z' /%3E%3Cpath fill-rule='evenodd' d='M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

.time-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

.pin-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath d='M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z' /%3E%3C/svg%3E");
}

.edit-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath d='M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z' /%3E%3C/svg%3E");
}

.delete-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

/* 响应式设计 */
@media (max-width: 768px) {
  .discussion-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 会话列表样式 */
.session-section {
  margin-top: 2rem;
  background-color: var(--background-color, #ffffff);
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.session-section .section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  background-color: var(--header-background, #f9fafb);
}

.session-section .section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary, #1f2937);
  margin: 0;
}

.session-section .section-actions {
  display: flex;
  gap: 0.5rem;
}

.session-list {
  padding: 1rem;
}

.session-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 0.5rem;
  margin-bottom: 0.75rem;
  background-color: var(--background-color, #ffffff);
  cursor: pointer;
  transition: all 0.2s ease;
}

.session-item:hover {
  border-color: var(--primary-color, #3b82f6);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.session-item:last-child {
  margin-bottom: 0;
}

.session-info {
  flex: 1;
}

.session-name {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary, #1f2937);
  margin: 0 0 0.25rem 0;
}

.session-remark {
  font-size: 0.875rem;
  color: var(--text-secondary, #6b7280);
  margin: 0 0 0.5rem 0;
  line-height: 1.4;
}

.session-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.75rem;
  color: var(--text-tertiary, #9ca3af);
}

.session-actions {
  display: flex;
  gap: 0.5rem;
}

.session-actions .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

.text-danger {
  color: var(--danger-color, #ef4444) !important;
}

.text-danger:hover {
  color: var(--danger-hover, #dc2626) !important;
}

/* 刷新图标 */
.refresh-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

/* 加载状态样式 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--text-secondary, #6b7280);
}

.loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 2px solid var(--border-color, #e5e7eb);
  border-top: 2px solid var(--primary-color, #3b82f6);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 - 会话列表 */
@media (max-width: 768px) {
  .session-section .section-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .session-item {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .session-actions {
    justify-content: flex-end;
  }

  .session-meta {
    flex-direction: column;
    gap: 0.25rem;
  }
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
}

.modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color, #111827);
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-color-secondary, #6b7280);
  cursor: pointer;
  padding: 0.25rem;
  line-height: 1;
  transition: color 0.2s;
}

.modal-close:hover {
  color: var(--text-color, #111827);
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1.5rem;
  border-top: 1px solid var(--border-color, #e5e7eb);
  background-color: var(--background-color-secondary, #f9fafb);
}

/* 表单样式 */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-color, #111827);
  margin-bottom: 0.5rem;
}

.required {
  color: #ef4444;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color, #d1d5db);
  border-radius: 0.5rem;
  font-size: 0.875rem;
  transition: border-color 0.2s, box-shadow 0.2s;
  box-sizing: border-box;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-color, #6366f1);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

/* 成员选择样式 */
.member-selection {
  border: 1px solid var(--border-color, #d1d5db);
  border-radius: 0.5rem;
  overflow: hidden;
}

.search-box {
  padding: 0.75rem;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  background-color: var(--background-color-secondary, #f9fafb);
}

.member-list {
  max-height: 300px;
  overflow-y: auto;
}

.member-list-header {
  padding: 0.75rem;
  background-color: var(--background-color-secondary, #f9fafb);
  border-bottom: 1px solid var(--border-color, #e5e7eb);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
}

.member-items {
  padding: 0.5rem 0;
}

.member-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.member-item:hover {
  background-color: var(--background-color-secondary, #f9fafb);
}

.member-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.member-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-color, #111827);
}

.member-detail {
  font-size: 0.75rem;
  color: var(--text-color-secondary, #6b7280);
}

.loading-members,
.no-members {
  padding: 2rem;
  text-align: center;
  color: var(--text-color-secondary, #6b7280);
}

.loading-spinner-small {
  width: 1rem;
  height: 1rem;
  border: 2px solid var(--border-color, #e5e7eb);
  border-top: 2px solid var(--primary-color, #6366f1);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  display: inline-block;
  margin-right: 0.5rem;
}

.selected-count {
  padding: 0.75rem;
  background-color: var(--background-color-secondary, #f9fafb);
  border-top: 1px solid var(--border-color, #e5e7eb);
  font-size: 0.875rem;
  color: var(--primary-color, #6366f1);
  font-weight: 500;
}

/* 会话信息显示样式 */
.session-info-display {
  background-color: var(--background-color-secondary, #f9fafb);
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 0.5rem;
  padding: 1rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-color-secondary, #6b7280);
}

.info-value {
  font-size: 0.875rem;
  color: var(--text-color, #111827);
  font-weight: 400;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 0.5rem;
  }

  .modal-content {
    max-height: 95vh;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 1rem;
  }

  .member-list {
    max-height: 200px;
  }
}
</style>