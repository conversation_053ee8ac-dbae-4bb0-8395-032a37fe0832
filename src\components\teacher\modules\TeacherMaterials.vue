<template>
  <div class="materials-panel">
    <!-- 资料库操作按钮 -->
    <div class="materials-actions">
      <div class="actions-right">
        <button class="btn btn-outline" @click="createNewFolder">
          <i class="btn-icon folder-icon"></i>
          新建文件夹
        </button>
        <button class="btn btn-primary" @click="uploadMaterials">
          <i class="btn-icon upload-icon"></i>
          上传资料
        </button>
      </div>
    </div>

    <!-- 资料分类网格 -->
    <div class="materials-grid">
      <div class="material-category-card course-materials-card" @click="viewCoursesMaterials">
        <div class="category-icon">
          📁
        </div>
        <div class="category-info">
          <h3 class="category-title">课程资料</h3>
          <p class="category-count">{{ courseMaterials.count }}个文件</p>
        </div>
      </div>
      
      <div class="material-category-card lesson-templates-card" @click="viewLessonTemplates">
        <div class="category-icon">
          📄
        </div>
        <div class="category-info">
          <h3 class="category-title">教案模板</h3>
          <p class="category-count">{{ lessonTemplates.count }}个文件</p>
        </div>
      </div>
      
      <div class="material-category-card video-resources-card" @click="viewVideoResources">
        <div class="category-icon">
          🎥
        </div>
        <div class="category-info">
          <h3 class="category-title">视频资源</h3>
          <p class="category-count">{{ videoResources.count }}个文件</p>
        </div>
      </div>
      
      <div class="material-category-card resource-sharing-card" @click="handleResourceSharing">
        <div class="category-icon">
          📤
        </div>
        <div class="category-info">
          <h3 class="category-title">资源共享</h3>
          <p class="category-count">分享学习资料</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

// 资料库数据
const courseMaterials = ref({
  count: 25,
  files: []
});

const lessonTemplates = ref({
  count: 12,
  files: []
});

const videoResources = ref({
  count: 8,
  files: []
});

// 资料库相关方法
const createNewFolder = () => {
  console.log('新建文件夹');
  // TODO: 显示新建文件夹对话框
};

const uploadMaterials = () => {
  console.log('上传资料');
  // TODO: 显示文件上传对话框
};

const viewCoursesMaterials = () => {
  console.log('查看课程资料');
  // TODO: 跳转到课程资料列表
};

const viewLessonTemplates = () => {
  console.log('查看教案模板');
  // TODO: 跳转到教案模板列表
};

const viewVideoResources = () => {
  console.log('查看视频资源');
  // TODO: 跳转到视频资源列表
};

const handleResourceSharing = () => {
  console.log('管理资源');
  // TODO: 跳转到资源共享页面
};
</script>

<style scoped>
/* 资料库面板样式 */
.materials-panel {
  flex: 1;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 资料库样式 */
.materials-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
}

.materials-actions .actions-right {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.materials-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
}

.material-category-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.material-category-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
  border-color: #d1d5db;
}

.category-icon {
  font-size: 2.5rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 10px;
  flex-shrink: 0;
}

.category-info {
  flex: 1;
}

.category-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
}

.category-count {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

/* 按钮样式 */
.btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  white-space: nowrap;
  text-transform: none;
  letter-spacing: 0.025em;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
  color: #fff;
  box-shadow: 0 2px 4px rgba(66, 153, 225, 0.3);
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(66, 153, 225, 0.4);
}

.btn-outline {
  background: transparent;
  border: 2px solid rgba(66, 153, 225, 0.5);
  color: #4299e1;
}

.btn-outline:hover {
  background: rgba(66, 153, 225, 0.1);
  border-color: #4299e1;
  transform: translateY(-1px);
}

.btn-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 0.5rem;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  vertical-align: middle;
}

.folder-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z'/%3E%3C/svg%3E");
}

.upload-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23fff'%3E%3Cpath d='M9 16h6v-6h4l-7-7-7 7h4zm-4 2h14v2H5z'/%3E%3C/svg%3E");
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .materials-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.25rem;
  }
}

@media (max-width: 768px) {
  .materials-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
  
  .materials-actions {
    justify-content: flex-start;
  }
  
  .materials-actions .actions-right {
    width: 100%;
    justify-content: flex-start;
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }
  
  .materials-actions .btn {
    width: 100%;
  }
  
  .material-category-card {
    padding: 1.25rem;
  }
  
  .category-icon {
    font-size: 2rem;
    width: 50px;
    height: 50px;
  }
}

@media (max-width: 600px) {
  .materials-grid {
    grid-template-columns: 1fr;
  }
}
</style>
