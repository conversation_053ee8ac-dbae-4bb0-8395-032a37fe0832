<template>
  <div class="teacher-mypage">
    <!-- 页面头部 -->
            <div class="page-header">
          <button
            class="back-to-home-btn"
            @click.stop.prevent="handleBackToHome"
            @mousedown="() => console.log('按钮被按下')"
            @mouseup="() => console.log('按钮被释放')"
            type="button"
          >
            <i class="back-icon"></i>
            <span>返回首页</span>
          </button>
          <div class="header-content">
            <h1 class="page-title">AI智能OBE智慧教学平台</h1>
          </div>
        </div>
    
    <!-- 主要内容布局 -->
    <div class="content-layout">
      <!-- 左侧导航菜单 -->
      <div class="sidebar">
        <div class="sidebar-menu">
          <div 
            v-for="tab in menuTabs" 
            :key="tab.key"
            class="menu-item" 
            :class="{ active: activeTab === tab.key }" 
            @click="switchTab(tab.key)"
          >
            <i class="menu-icon" :class="tab.icon"></i>
            <span class="menu-text">{{ tab.label }}</span>
          </div>
        </div>
      </div>
      
      <!-- 主内容区域 -->
      <div class="main-content">
        <!-- 知识图谱模块 -->
        <TeacherKnowledgeGraph 
          v-if="activeTab === 'knowledge-graph'" 
          @refresh="handleRefresh"
        />
        
        <!-- 课程管理模块 -->
        <TeacherCourses 
          v-if="activeTab === 'courses'"
          @refresh-classes="handleRefreshClasses"
        />
        
        <!-- 教学助手模块 -->
        <TeacherLessonAssistant 
          v-if="activeTab === 'lesson-assistant'"
        />
        
 

        
        <!-- AI助手模块 -->
        <TeacherAIAssistant 
          v-if="activeTab === 'ai-assistant'"
        />
        
        <!-- 资料库模块 -->
        <TeacherMaterials 
          v-if="activeTab === 'materials'"
        />
        
        <!-- 设置模块 -->
        <TeacherSettings 
          v-if="activeTab === 'settings'"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';

// 导入拆分的组件
import TeacherKnowledgeGraph from '@/components/teacher/modules/TeacherKnowledgeGraph.vue';
import TeacherCourses from '@/components/teacher/modules/TeacherCourses.vue';
import TeacherLessonAssistant from '@/components/teacher/modules/TeacherLessonAssistant.vue';

import TeacherAIAssistant from '@/components/teacher/modules/TeacherAIAssistant.vue';
import TeacherMaterials from '@/components/teacher/modules/TeacherMaterials.vue';
import TeacherSettings from '@/components/teacher/modules/TeacherSettings.vue';

// 导入样式
import '@/styles/teacher/variables.css';
import '@/styles/teacher/common.css';
import '@/styles/teacher/components.css';
import '@/styles/teacher/layouts.css';

const router = useRouter();

// 当前激活的标签页
const activeTab = ref('knowledge-graph');

// 组件引用
const classManagementRef = ref(null);

// 菜单标签页配置
const menuTabs = [
  {
    key: 'knowledge-graph',
    label: '知识图谱',
    icon: 'knowledge-graph-icon'
  },
  {
    key: 'courses',
    label: '课程',
    icon: 'courses-icon'
  },
  {
    key: 'lesson-assistant',
    label: '教学助手',
    icon: 'lesson-assistant-icon'
  },
  // {
  //   key: 'student-management',
  //   label: '班级管理',
  //   icon: 'student-management-icon'
  // },

  {
    key: 'ai-assistant',
    label: 'AI助手',
    icon: 'ai-assistant-icon'
  },
  {
    key: 'materials',
    label: '资料库',
    icon: 'materials-icon'
  },
  {
    key: 'settings',
    label: '设置',
    icon: 'settings-icon'
  }
];

// 切换标签页
const switchTab = (tabKey) => {
  console.log('切换到标签页:', tabKey);
  activeTab.value = tabKey;
  
  // 保存当前标签页到本地存储
  localStorage.setItem('teacher-active-tab', tabKey);
};

// 处理刷新事件
const handleRefresh = () => {
  console.log('刷新数据');
  // 可以在这里添加全局刷新逻辑
};

// 处理班级刷新事件
const handleRefreshClasses = () => {
  console.log('刷新班级列表');
  // 刷新班级管理组件的数据
  if (classManagementRef.value && classManagementRef.value.fetchClasses) {
    classManagementRef.value.fetchClasses();
  }
};

// 处理返回首页
const handleBackToHome = (event) => {
  console.log('返回首页按钮被点击', event);

  // 阻止默认行为和事件冒泡
  if (event) {
    event.preventDefault();
    event.stopPropagation();
  }

  try {
    // 清除当前页面状态
    localStorage.removeItem('teacher-active-tab');
    console.log('已清除本地存储');

    // 使用replace方法强制跳转到首页
    console.log('准备跳转到首页');
    router.replace('/').then(() => {
      console.log('路由跳转成功');
      // 确保页面滚动到顶部
      window.scrollTo(0, 0);
    }).catch((error) => {
      console.error('路由跳转失败:', error);
      // 如果路由跳转失败，尝试使用window.location
      window.location.href = '/';
    });
  } catch (error) {
    console.error('返回首页处理出错:', error);
    // 最后的备用方案
    window.location.href = '/';
  }
};

// 组件挂载时的初始化
onMounted(() => {
  // 从本地存储恢复上次的标签页
  const savedTab = localStorage.getItem('teacher-active-tab');
  if (savedTab && menuTabs.some(tab => tab.key === savedTab)) {
    activeTab.value = savedTab;
  }
  
  console.log('教师个人中心页面已加载，当前标签页:', activeTab.value);
});
</script>

<style scoped>
/* 教师个人中心页面样式 */
.teacher-mypage {
  min-height: 100vh;
  background-color: var(--background-color-secondary, #f9fafb);
  font-family: var(--font-family-sans, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif);
}

/* 页面头部样式 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.75rem 2rem;
  text-align: center;
  position: relative;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.back-to-home-btn {
  position: absolute;
  left: 2rem;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  color: white;
  text-decoration: none;
  font-size: 0.9375rem;
  font-weight: 500;
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: none;
  cursor: pointer;
  font-family: inherit;
  z-index: 1000;
  pointer-events: auto;
  user-select: none;
}

.back-to-home-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-50%) translateX(-2px);
}

.back-icon {
  margin-right: 0.375rem;
  width: 16px;
  height: 16px;
  position: relative;
}

.back-icon::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  width: 10px;
  height: 10px;
  border-left: 2px solid white;
  border-bottom: 2px solid white;
  transform: translateY(-50%) rotate(45deg);
}

.page-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-subtitle {
  font-size: 1rem;
  opacity: 0.9;
  font-weight: 400;
}

/* 内容布局样式 */
.content-layout {
  display: flex;
  min-height: calc(100vh - 100px);
  background-color: var(--background-color, #ffffff);
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.05);
}

/* 侧边栏样式 */
.sidebar {
  width: 180px;
  background: linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%);
  border-right: 1px solid var(--border-color, #e5e7eb);
  flex-shrink: 0;
}

.sidebar-menu {
  padding: 1.5rem 0;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 1rem 1rem;
  color: var(--text-color-secondary, #6b7280);
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
  font-weight: 500;
}

.menu-item:hover {
  background: rgba(66, 153, 225, 0.05);
  color: var(--primary-color, #4299e1);
  border-left-color: rgba(66, 153, 225, 0.3);
}

.menu-item.active {
  background: rgba(66, 153, 225, 0.1);
  color: var(--primary-color, #4299e1);
  border-left-color: var(--primary-color, #4299e1);
  font-weight: 600;
}

.menu-icon {
  width: 18px;
  height: 18px;
  margin-right: 0.5rem;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.menu-item.active .menu-icon,
.menu-item:hover .menu-icon {
  opacity: 1;
}

.menu-text {
  font-size: 0.875rem;
  line-height: 1.4;
}

/* 主内容区域样式 */
.main-content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  background-color: var(--background-color-secondary, #f9fafb);
}

/* 菜单图标样式 */
.knowledge-graph-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z'/%3E%3C/svg%3E");
}

.courses-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M12 3L1 9l4 2.18v6L12 21l7-3.82v-6l2-1.09V17h2V9L12 3zm6.82 6L12 12.72 5.18 9 12 5.28 18.82 9zM17 15.99l-5 2.73-5-2.73v-3.72L12 15l5-2.73v3.72z'/%3E%3C/svg%3E");
}

.lesson-assistant-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E");
}

.student-management-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A1.998 1.998 0 0 0 18 7h-2c-.8 0-1.54.37-2.01 1.01l-.74 2.22-2.99-2.99C10.05 6.85 9.49 6.67 8.9 6.67c-1.66 0-3 1.34-3 3 0 .59.18 1.15.5 1.61L9 14.89V22h2v-6h2v6h4z'/%3E%3C/svg%3E");
}

.homework-discussion-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4v3c0 .6.4 1 1 1h.5c.2 0 .5-.1.7-.3L14.6 18H20c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z'/%3E%3C/svg%3E");
}

.ai-assistant-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z'/%3E%3C/svg%3E");
}

.materials-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z'/%3E%3C/svg%3E");
}

.settings-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.82,11.69,4.82,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z'/%3E%3C/svg%3E");
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .content-layout {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    order: 2;
  }
  
  .sidebar-menu {
    display: flex;
    overflow-x: auto;
    padding: 1rem;
    gap: 0.5rem;
  }
  
  .menu-item {
    flex-shrink: 0;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    border-left: none;
    white-space: nowrap;
  }
  
  .main-content {
    order: 1;
    padding: 1.5rem;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 0.5rem 1rem;
  }
  
  .page-title {
    font-size: 1.25rem;
  }
  
  .back-to-home-btn {
    position: static;
    transform: none;
    margin-bottom: 1rem;
    align-self: flex-start;
  }
  
  .main-content {
    padding: 1rem;
  }
  
  .sidebar-menu {
    padding: 0.75rem;
  }
  
  .menu-item {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }
  
  .menu-icon {
    width: 16px;
    height: 16px;
    margin-right: 0.5rem;
  }
}
</style>
