<template>
  <div class="course-analytics">
    <!-- 页面标题 -->
    <div class="section-header">
      <h2 class="section-title">学情分析</h2>
      <div class="section-actions">
        <button class="btn btn-primary" @click="generateReport">
          <i class="btn-icon report-icon"></i>
          生成报告
        </button>
        <button class="btn btn-secondary" @click="exportData">
          <i class="btn-icon export-icon"></i>
          导出数据
        </button>
      </div>
    </div>

    <!-- 总体统计 -->
    <div class="overview-stats">
      <div class="stat-card primary">
        <div class="stat-icon">
          <i class="students-icon"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ overviewStats.totalStudents }}</div>
          <div class="stat-label">总学生数</div>
        </div>
      </div>
      <div class="stat-card success">
        <div class="stat-icon">
          <i class="active-icon"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ overviewStats.activeStudents }}</div>
          <div class="stat-label">活跃学生</div>
        </div>
      </div>
      <div class="stat-card warning">
        <div class="stat-icon">
          <i class="average-icon"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ overviewStats.averageScore }}分</div>
          <div class="stat-label">平均成绩</div>
        </div>
      </div>
      <div class="stat-card info">
        <div class="stat-icon">
          <i class="completion-icon"></i>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ overviewStats.completionRate }}%</div>
          <div class="stat-label">完成率</div>
        </div>
      </div>
    </div>

    <!-- 分析图表区域 -->
    <div class="analytics-grid">
      <!-- 学习进度分析 -->
      <div class="analytics-card">
        <div class="card-header">
          <h3 class="card-title">学习进度分析</h3>
          <div class="card-actions">
            <select v-model="progressTimeRange" class="time-select">
              <option value="week">本周</option>
              <option value="month">本月</option>
              <option value="semester">本学期</option>
            </select>
          </div>
        </div>
        <div class="card-content">
          <div class="progress-chart">
            <div class="chart-placeholder">
              <i class="chart-icon"></i>
              <p>学习进度趋势图</p>
            </div>
          </div>
          <div class="progress-summary">
            <div class="summary-item">
              <span class="summary-label">按时完成</span>
              <span class="summary-value success">{{ progressStats.onTime }}人</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">延迟完成</span>
              <span class="summary-value warning">{{ progressStats.delayed }}人</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">未完成</span>
              <span class="summary-value danger">{{ progressStats.incomplete }}人</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 成绩分布分析 -->
      <div class="analytics-card">
        <div class="card-header">
          <h3 class="card-title">成绩分布分析</h3>
          <div class="card-actions">
            <select v-model="scoreType" class="score-select">
              <option value="homework">作业成绩</option>
              <option value="exam">考试成绩</option>
              <option value="overall">综合成绩</option>
            </select>
          </div>
        </div>
        <div class="card-content">
          <div class="score-chart">
            <div class="chart-placeholder">
              <i class="chart-icon"></i>
              <p>成绩分布柱状图</p>
            </div>
          </div>
          <div class="score-distribution">
            <div class="distribution-item">
              <span class="grade-range">90-100分</span>
              <div class="grade-bar">
                <div class="grade-fill excellent" :style="`width: ${scoreDistribution.excellent}%`"></div>
              </div>
              <span class="grade-count">{{ Math.round(scoreDistribution.excellent * overviewStats.totalStudents / 100) }}人</span>
            </div>
            <div class="distribution-item">
              <span class="grade-range">80-89分</span>
              <div class="grade-bar">
                <div class="grade-fill good" :style="`width: ${scoreDistribution.good}%`"></div>
              </div>
              <span class="grade-count">{{ Math.round(scoreDistribution.good * overviewStats.totalStudents / 100) }}人</span>
            </div>
            <div class="distribution-item">
              <span class="grade-range">70-79分</span>
              <div class="grade-bar">
                <div class="grade-fill average" :style="`width: ${scoreDistribution.average}%`"></div>
              </div>
              <span class="grade-count">{{ Math.round(scoreDistribution.average * overviewStats.totalStudents / 100) }}人</span>
            </div>
            <div class="distribution-item">
              <span class="grade-range">60-69分</span>
              <div class="grade-bar">
                <div class="grade-fill poor" :style="`width: ${scoreDistribution.poor}%`"></div>
              </div>
              <span class="grade-count">{{ Math.round(scoreDistribution.poor * overviewStats.totalStudents / 100) }}人</span>
            </div>
            <div class="distribution-item">
              <span class="grade-range">60分以下</span>
              <div class="grade-bar">
                <div class="grade-fill fail" :style="`width: ${scoreDistribution.fail}%`"></div>
              </div>
              <span class="grade-count">{{ Math.round(scoreDistribution.fail * overviewStats.totalStudents / 100) }}人</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 学习活跃度分析 -->
      <div class="analytics-card">
        <div class="card-header">
          <h3 class="card-title">学习活跃度分析</h3>
        </div>
        <div class="card-content">
          <div class="activity-metrics">
            <div class="metric-item">
              <div class="metric-icon">
                <i class="login-icon"></i>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ activityStats.avgLoginFreq }}</div>
                <div class="metric-label">平均登录频次/周</div>
              </div>
            </div>
            <div class="metric-item">
              <div class="metric-icon">
                <i class="study-time-icon"></i>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ activityStats.avgStudyTime }}h</div>
                <div class="metric-label">平均学习时长/周</div>
              </div>
            </div>
            <div class="metric-item">
              <div class="metric-icon">
                <i class="interaction-icon"></i>
              </div>
              <div class="metric-content">
                <div class="metric-value">{{ activityStats.avgInteractions }}</div>
                <div class="metric-label">平均互动次数/周</div>
              </div>
            </div>
          </div>
          <div class="activity-chart">
            <div class="chart-placeholder">
              <i class="chart-icon"></i>
              <p>活跃度热力图</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 知识点掌握情况 -->
      <div class="analytics-card">
        <div class="card-header">
          <h3 class="card-title">知识点掌握情况</h3>
        </div>
        <div class="card-content">
          <div class="knowledge-points">
            <div
              v-for="point in knowledgePoints"
              :key="point.id"
              class="knowledge-item"
            >
              <div class="knowledge-header">
                <span class="knowledge-name">{{ point.name }}</span>
                <span class="knowledge-score" :class="getScoreClass(point.masteryRate)">
                  {{ point.masteryRate }}%
                </span>
              </div>
              <div class="knowledge-progress">
                <div class="progress-bar">
                  <div
                    class="progress-fill"
                    :class="getScoreClass(point.masteryRate)"
                    :style="`width: ${point.masteryRate}%`"
                  ></div>
                </div>
              </div>
              <div class="knowledge-details">
                <span class="detail-item">掌握：{{ point.mastered }}人</span>
                <span class="detail-item">待提高：{{ point.needImprovement }}人</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 学生个体分析 -->
    <div class="student-analysis">
      <div class="section-header">
        <h3 class="section-title">学生个体分析</h3>
        <div class="search-box">
          <input
            v-model="studentSearchQuery"
            type="text"
            placeholder="搜索学生..."
            class="search-input"
          >
          <i class="search-icon"></i>
        </div>
      </div>

      <div class="students-table">
        <div class="table-header">
          <div class="header-cell">姓名</div>
          <div class="header-cell">学号</div>
          <div class="header-cell">平均成绩</div>
          <div class="header-cell">完成率</div>
          <div class="header-cell">活跃度</div>
          <div class="header-cell">操作</div>
        </div>
        <div class="table-body">
          <div
            v-for="student in filteredStudents"
            :key="student.id"
            class="table-row"
          >
            <div class="table-cell">
              <div class="student-info">
                <img :src="student.avatar" :alt="student.name" class="student-avatar">
                <span class="student-name">{{ student.name }}</span>
              </div>
            </div>
            <div class="table-cell">{{ student.studentId }}</div>
            <div class="table-cell">
              <span :class="['score-badge', getScoreClass(student.averageScore)]">
                {{ student.averageScore }}分
              </span>
            </div>
            <div class="table-cell">
              <span :class="['rate-badge', getRateClass(student.completionRate)]">
                {{ student.completionRate }}%
              </span>
            </div>
            <div class="table-cell">
              <span :class="['activity-badge', getActivityClass(student.activityLevel)]">
                {{ getActivityText(student.activityLevel) }}
              </span>
            </div>
            <div class="table-cell">
              <button class="action-btn" @click="viewStudentDetail(student)">
                <i class="view-icon"></i>
                详情
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 定义props
const props = defineProps({
  courseId: {
    type: [String, Number],
    required: true
  }
});

// 定义emits
const emit = defineEmits(['refresh']);

// 响应式数据
const loading = ref(false);
const progressTimeRange = ref('month');
const scoreType = ref('overall');
const studentSearchQuery = ref('');

// 总体统计数据
const overviewStats = ref({
  totalStudents: 38,
  activeStudents: 32,
  averageScore: 78.5,
  completionRate: 84
});

// 学习进度统计
const progressStats = ref({
  onTime: 28,
  delayed: 7,
  incomplete: 3
});

// 成绩分布
const scoreDistribution = ref({
  excellent: 18, // 90-100分
  good: 32,      // 80-89分
  average: 26,   // 70-79分
  poor: 16,      // 60-69分
  fail: 8        // 60分以下
});

// 活跃度统计
const activityStats = ref({
  avgLoginFreq: 4.2,
  avgStudyTime: 6.8,
  avgInteractions: 12.5
});

// 知识点掌握情况
const knowledgePoints = ref([
  {
    id: 1,
    name: '静力平衡原理',
    masteryRate: 85,
    mastered: 32,
    needImprovement: 6
  },
  {
    id: 2,
    name: '几何组成分析',
    masteryRate: 78,
    mastered: 30,
    needImprovement: 8
  },
  {
    id: 3,
    name: '静定梁内力计算',
    masteryRate: 72,
    mastered: 27,
    needImprovement: 11
  },
  {
    id: 4,
    name: '静定刚架分析',
    masteryRate: 65,
    mastered: 25,
    needImprovement: 13
  },
  {
    id: 5,
    name: '桁架内力计算',
    masteryRate: 58,
    mastered: 22,
    needImprovement: 16
  },
  {
    id: 6,
    name: '位移计算方法',
    masteryRate: 45,
    mastered: 17,
    needImprovement: 21
  }
]);

// 学生列表
const students = ref([
  {
    id: 1,
    name: '张三',
    studentId: '2021001',
    avatar: '/api/placeholder/32/32',
    averageScore: 92,
    completionRate: 95,
    activityLevel: 'high'
  },
  {
    id: 2,
    name: '李四',
    studentId: '2021002',
    avatar: '/api/placeholder/32/32',
    averageScore: 85,
    completionRate: 88,
    activityLevel: 'high'
  },
  {
    id: 3,
    name: '王五',
    studentId: '2021003',
    avatar: '/api/placeholder/32/32',
    averageScore: 78,
    completionRate: 82,
    activityLevel: 'medium'
  },
  {
    id: 4,
    name: '赵六',
    studentId: '2021004',
    avatar: '/api/placeholder/32/32',
    averageScore: 65,
    completionRate: 70,
    activityLevel: 'low'
  },
  {
    id: 5,
    name: '钱七',
    studentId: '2021005',
    avatar: '/api/placeholder/32/32',
    averageScore: 88,
    completionRate: 90,
    activityLevel: 'high'
  }
]);

// 过滤后的学生列表
const filteredStudents = computed(() => {
  if (!studentSearchQuery.value.trim()) {
    return students.value;
  }

  const query = studentSearchQuery.value.toLowerCase();
  return students.value.filter(student =>
    student.name.toLowerCase().includes(query) ||
    student.studentId.toLowerCase().includes(query)
  );
});

// 获取成绩等级样式类
const getScoreClass = (score) => {
  if (score >= 90) return 'excellent';
  if (score >= 80) return 'good';
  if (score >= 70) return 'average';
  if (score >= 60) return 'poor';
  return 'fail';
};

// 获取完成率样式类
const getRateClass = (rate) => {
  if (rate >= 90) return 'excellent';
  if (rate >= 80) return 'good';
  if (rate >= 70) return 'average';
  return 'poor';
};

// 获取活跃度样式类
const getActivityClass = (level) => {
  return level;
};

// 获取活跃度文本
const getActivityText = (level) => {
  const textMap = {
    'high': '高',
    'medium': '中',
    'low': '低'
  };
  return textMap[level] || '未知';
};

// 生成报告
const generateReport = () => {
  console.log('生成学情分析报告');
  // TODO: 实现生成报告逻辑
};

// 导出数据
const exportData = () => {
  console.log('导出学情数据');
  // TODO: 实现导出数据逻辑
};

// 查看学生详情
const viewStudentDetail = (student) => {
  console.log('查看学生详情:', student);
  // TODO: 实现查看学生详情逻辑
};

// 组件挂载时加载数据
onMounted(() => {
  console.log('学情分析组件加载，课程ID:', props.courseId);
  // TODO: 加载实际数据
});
</script>

<style scoped>
/* 学情分析样式 */
.course-analytics {
  background-color: var(--background-color, #ffffff);
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  background-color: var(--background-color, #ffffff);
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color, #1f2937);
  margin: 0;
}

.section-actions {
  display: flex;
  gap: 0.75rem;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.btn-primary {
  background-color: var(--primary-color, #6366f1);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-color-dark, #4f46e5);
}

.btn-secondary {
  background-color: var(--background-color-secondary, #f3f4f6);
  color: var(--text-color, #374151);
  border-color: var(--border-color, #d1d5db);
}

.btn-secondary:hover {
  background-color: var(--background-color-tertiary, #e5e7eb);
}

/* 总体统计 */
.overview-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  padding: 1.5rem;
  background-color: var(--background-color-secondary, #f9fafb);
  border-bottom: 1px solid var(--border-color, #e5e7eb);
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-left: 4px solid;
}

.stat-card.primary {
  border-left-color: #6366f1;
}

.stat-card.success {
  border-left-color: #10b981;
}

.stat-card.warning {
  border-left-color: #f59e0b;
}

.stat-card.info {
  border-left-color: #06b6d4;
}

.stat-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stat-card.primary .stat-icon {
  background-color: rgba(99, 102, 241, 0.1);
  color: #6366f1;
}

.stat-card.success .stat-icon {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.stat-card.warning .stat-icon {
  background-color: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.stat-card.info .stat-icon {
  background-color: rgba(6, 182, 212, 0.1);
  color: #06b6d4;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-color, #1f2937);
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-color-secondary, #6b7280);
}

/* 分析图表网格 */
.analytics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  padding: 1.5rem;
}

.analytics-card {
  background-color: white;
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 0.5rem;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  background-color: var(--background-color-secondary, #f9fafb);
}

.card-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color, #1f2937);
  margin: 0;
}

.card-actions {
  display: flex;
  gap: 0.5rem;
}

.time-select, .score-select {
  padding: 0.375rem 0.75rem;
  border: 1px solid var(--border-color, #d1d5db);
  border-radius: 0.375rem;
  font-size: 0.875rem;
  background-color: white;
}

.card-content {
  padding: 1.5rem;
}

/* 图表占位符 */
.chart-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  background-color: var(--background-color-secondary, #f9fafb);
  border: 2px dashed var(--border-color, #d1d5db);
  border-radius: 0.5rem;
  color: var(--text-color-secondary, #6b7280);
  margin-bottom: 1rem;
}

.chart-icon {
  width: 3rem;
  height: 3rem;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%236b7280'%3E%3Cpath d='M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z' /%3E%3C/svg%3E");
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  margin-bottom: 0.5rem;
}

/* 进度摘要 */
.progress-summary {
  display: flex;
  justify-content: space-around;
  gap: 1rem;
}

.summary-item {
  text-align: center;
}

.summary-label {
  display: block;
  font-size: 0.875rem;
  color: var(--text-color-secondary, #6b7280);
  margin-bottom: 0.25rem;
}

.summary-value {
  font-size: 1.25rem;
  font-weight: 600;
}

.summary-value.success {
  color: #10b981;
}

.summary-value.warning {
  color: #f59e0b;
}

.summary-value.danger {
  color: #ef4444;
}

/* 成绩分布 */
.score-distribution {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.distribution-item {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.grade-range {
  min-width: 5rem;
  font-size: 0.875rem;
  color: var(--text-color-secondary, #6b7280);
}

.grade-bar {
  flex: 1;
  height: 1.5rem;
  background-color: var(--background-color-secondary, #f3f4f6);
  border-radius: 0.75rem;
  overflow: hidden;
}

.grade-fill {
  height: 100%;
  border-radius: 0.75rem;
  transition: width 0.3s ease;
}

.grade-fill.excellent {
  background-color: #10b981;
}

.grade-fill.good {
  background-color: #06b6d4;
}

.grade-fill.average {
  background-color: #f59e0b;
}

.grade-fill.poor {
  background-color: #f97316;
}

.grade-fill.fail {
  background-color: #ef4444;
}

.grade-count {
  min-width: 3rem;
  text-align: right;
  font-size: 0.875rem;
  color: var(--text-color-secondary, #6b7280);
}

/* 活跃度指标 */
.activity-metrics {
  display: flex;
  justify-content: space-around;
  margin-bottom: 1.5rem;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  text-align: center;
}

.metric-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background-color: var(--primary-color, #6366f1);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.metric-content {
  text-align: left;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-color, #1f2937);
}

.metric-label {
  font-size: 0.75rem;
  color: var(--text-color-secondary, #6b7280);
}

/* 知识点掌握 */
.knowledge-points {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.knowledge-item {
  padding: 1rem;
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 0.5rem;
  background-color: var(--background-color-secondary, #f9fafb);
}

.knowledge-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.knowledge-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-color, #1f2937);
}

.knowledge-score {
  font-size: 0.875rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

.knowledge-score.excellent {
  background-color: #dcfce7;
  color: #166534;
}

.knowledge-score.good {
  background-color: #dbeafe;
  color: #1e40af;
}

.knowledge-score.average {
  background-color: #fef3c7;
  color: #92400e;
}

.knowledge-score.poor, .knowledge-score.fail {
  background-color: #fee2e2;
  color: #991b1b;
}

.knowledge-progress {
  margin-bottom: 0.5rem;
}

.progress-bar {
  height: 0.5rem;
  background-color: var(--background-color-tertiary, #e5e7eb);
  border-radius: 0.25rem;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 0.25rem;
  transition: width 0.3s ease;
}

.progress-fill.excellent {
  background-color: #10b981;
}

.progress-fill.good {
  background-color: #06b6d4;
}

.progress-fill.average {
  background-color: #f59e0b;
}

.progress-fill.poor, .progress-fill.fail {
  background-color: #ef4444;
}

.knowledge-details {
  display: flex;
  gap: 1rem;
}

.detail-item {
  font-size: 0.75rem;
  color: var(--text-color-secondary, #6b7280);
}

/* 学生分析 */
.student-analysis {
  margin-top: 1.5rem;
  border-top: 1px solid var(--border-color, #e5e7eb);
}

.student-analysis .section-header {
  border-bottom: none;
  padding-bottom: 1rem;
}

.search-box {
  position: relative;
  width: 300px;
}

.search-input {
  width: 100%;
  padding: 0.5rem 2.5rem 0.5rem 1rem;
  border: 1px solid var(--border-color, #d1d5db);
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: border-color 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color, #6366f1);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.search-icon {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1rem;
  height: 1rem;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%236b7280'%3E%3Cpath fill-rule='evenodd' d='M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z' clip-rule='evenodd' /%3E%3C/svg%3E");
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

/* 学生表格 */
.students-table {
  background-color: white;
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 0.5rem;
  overflow: hidden;
  margin: 0 1.5rem 1.5rem;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1fr;
  gap: 1rem;
  padding: 1rem 1.5rem;
  background-color: var(--background-color-secondary, #f9fafb);
  border-bottom: 1px solid var(--border-color, #e5e7eb);
}

.header-cell {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-color, #1f2937);
}

.table-body {
  display: flex;
  flex-direction: column;
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1fr;
  gap: 1rem;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  transition: background-color 0.2s;
}

.table-row:hover {
  background-color: var(--background-color-secondary, #f9fafb);
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  color: var(--text-color, #374151);
}

.student-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.student-avatar {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  object-fit: cover;
}

.student-name {
  font-weight: 500;
}

.score-badge, .rate-badge, .activity-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.score-badge.excellent, .rate-badge.excellent {
  background-color: #dcfce7;
  color: #166534;
}

.score-badge.good, .rate-badge.good {
  background-color: #dbeafe;
  color: #1e40af;
}

.score-badge.average, .rate-badge.average {
  background-color: #fef3c7;
  color: #92400e;
}

.score-badge.poor, .rate-badge.poor, .score-badge.fail {
  background-color: #fee2e2;
  color: #991b1b;
}

.activity-badge.high {
  background-color: #dcfce7;
  color: #166534;
}

.activity-badge.medium {
  background-color: #fef3c7;
  color: #92400e;
}

.activity-badge.low {
  background-color: #fee2e2;
  color: #991b1b;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.375rem 0.75rem;
  border: 1px solid var(--border-color, #d1d5db);
  border-radius: 0.25rem;
  background-color: white;
  color: var(--text-color, #374151);
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s;
}

.action-btn:hover {
  background-color: var(--background-color-secondary, #f3f4f6);
}

/* 图标样式 */
.btn-icon, .report-icon, .export-icon, .students-icon, .active-icon, .average-icon,
.completion-icon, .login-icon, .study-time-icon, .interaction-icon, .view-icon {
  width: 1rem;
  height: 1rem;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

.stat-icon .students-icon, .stat-icon .active-icon, .stat-icon .average-icon,
.stat-icon .completion-icon, .metric-icon .login-icon, .metric-icon .study-time-icon,
.metric-icon .interaction-icon {
  width: 1.5rem;
  height: 1.5rem;
}

.report-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v3.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V8z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

.export-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

.students-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath d='M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z' /%3E%3C/svg%3E");
}

.active-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

.average-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath d='M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z' /%3E%3C/svg%3E");
}

.completion-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

.login-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M3 3a1 1 0 011 1v12a1 1 0 11-2 0V4a1 1 0 011-1zm7.707 3.293a1 1 0 010 1.414L9.414 9H17a1 1 0 110 2H9.414l1.293 1.293a1 1 0 01-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

.study-time-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

.interaction-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

.view-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath d='M10 12a2 2 0 100-4 2 2 0 000 4z' /%3E%3Cpath fill-rule='evenodd' d='M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .analytics-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .overview-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .section-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .search-box {
    width: 100%;
  }

  .activity-metrics {
    flex-direction: column;
    gap: 1rem;
  }

  .table-header, .table-row {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .table-cell {
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color, #e5e7eb);
  }

  .table-cell:last-child {
    border-bottom: none;
  }

  .header-cell::before {
    content: attr(data-label) ': ';
    font-weight: normal;
    color: var(--text-color-secondary, #6b7280);
  }
}
</style>