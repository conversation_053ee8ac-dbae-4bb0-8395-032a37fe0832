<template>
  <div class="group-panel">
    <div class="group-list">
      <div
        v-for="group in groups"
        :key="group.id"
        class="group-item"
        @click="viewGroup(group.id)"
      >
        <div class="group-header">
          <h3 class="group-title">{{ group.name }}</h3>
          <div class="group-members">{{ group.memberCount }}人</div>
        </div>
        <div class="group-info">
          <div class="info-item">
            <span class="info-label">课程:</span>
            <span class="info-value">{{ group.courseName }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">组长:</span>
            <span class="info-value">{{ group.leader }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">创建时间:</span>
            <span class="info-value">{{ group.createTime }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

// 小组数据
const groups = ref([
  {
    id: 1,
    name: '结构力学学习小组',
    courseName: '结构力学',
    leader: '王同学',
    memberCount: 6,
    createTime: '2024-11-01'
  },
  {
    id: 2,
    name: '土木工程项目组',
    courseName: '土木工程概论',
    leader: '赵同学',
    memberCount: 8,
    createTime: '2024-10-15'
  }
]);

// 小组相关方法
const viewGroup = (groupId) => {
  console.log('查看小组:', groupId);
  // TODO: 跳转到小组详情页
};
</script>

<style scoped>
/* 小组面板样式 */
.group-panel {
  flex: 1;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 小组列表样式 */
.group-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  overflow-y: auto;
}

.group-item {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.group-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.group-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  flex: 1;
}

.group-members {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  background: #f3f4f6;
  color: #6b7280;
  margin-left: 1rem;
}

.group-info {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.info-label {
  font-size: 0.875rem;
  color: #6b7280;
}

.info-value {
  font-size: 0.875rem;
  font-weight: 500;
  color: #1f2937;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .group-info {
    flex-direction: column;
    gap: 0.5rem;
  }
}
</style>
