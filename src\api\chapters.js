import request from './axios';

/**
 * 获取课程详情
 * @param {string|number} courseId 课程ID
 * @returns {Promise<Object>} 课程详情数据
 */
export const getCourseById = (courseId) => {
  console.log(`正在获取课程详情: ${courseId}`);

  if (!courseId) {
    console.error('获取课程详情失败: 课程ID不能为空');
    return Promise.resolve({
      success: false,
      error: true,
      warn: false,
      empty: false,
      msg: '课程ID不能为空',
      data: null
    });
  }

  return request({
    url: `/core/courses/${courseId}`,
    method: 'get'
  }).then(response => {
    console.log(`获取课程${courseId}详情成功:`, response);
    console.log('响应类型:', typeof response);
    console.log('响应JSON:', JSON.stringify(response, null, 2));

    // 检查响应格式
    if (response && response.code === 200 && response.data) {
      console.log('课程详情数据:', response.data);
      // 返回课程详情数据，包含完整的响应信息
      return {
        ...response,
        // 为了向后兼容，也直接暴露data中的字段
        courseData: response.data
      };
    } else {
      console.warn('获取课程详情响应格式不符合预期:', response);
      return response;
    }
  }).catch(error => {
    console.error(`获取课程 ${courseId} 详情失败:`, error);
    return Promise.reject(error);
  });
};

/**
 * 修改课程
 * @param {Object} courseData 课程数据
 * @returns {Promise<Object>} 修改结果
 */
export const updateCourse = (courseData) => {
  console.log('正在修改课程...', courseData);
  console.log('原始课程数据JSON:', JSON.stringify(courseData, null, 2));

  // 验证必填字段
  if (!courseData.id) {
    console.error('修改课程失败: 课程ID不能为空');
    return Promise.resolve({
      success: false,
      error: true,
      msg: '课程ID不能为空',
      data: null
    });
  }

  // 构建请求数据，过滤掉空值
  const cleanData = {};
  Object.keys(courseData).forEach(key => {
    if (courseData[key] !== null && courseData[key] !== undefined && courseData[key] !== '') {
      cleanData[key] = courseData[key];
    }
  });

  console.log('清理后的课程数据JSON:', JSON.stringify(cleanData, null, 2));

  return request({
    url: '/core/courses',
    method: 'put',
    data: cleanData,
    headers: {
      'Content-Type': 'application/json'
    }
  }).then(response => {
    console.log('修改课程HTTP响应成功:', response);
    console.log('响应类型:', typeof response);
    console.log('响应JSON:', JSON.stringify(response, null, 2));

    // 检查响应格式
    if (response && typeof response === 'object') {
      // 根据接口文档，响应包含 error, success, warn, empty 字段
      if (response.hasOwnProperty('success')) {
        console.log('响应包含success字段:', response.success);
        return response;
      }
    }

    console.warn('修改课程响应格式不符合预期:', response);
    return response;
  }).catch(error => {
    console.error('修改课程失败:', error);
    return Promise.reject(error);
  });
};

/**
 * 创建新章节
 * @param {Object} planData 章节数据
 * @returns {Promise<Object>} 创建结果
 */
export const createLessonPlan = (planData) => {
  console.log('创建新章节:', planData);
  return request({
    url: '/tp/plan',
    method: 'post',
    data: planData
  }).then(response => {
    console.log('创建章节成功:', response);
    return response;
  }).catch(error => {
    console.error('创建章节失败:', error);
    return Promise.reject(error);
  });
};

/**
 * 根据ID获取章节详情
 * @param {string|number} planId 章节ID
 * @returns {Promise<Object>} 章节详情数据
 */
export const getLessonPlanById = (planId) => {
  console.log(`正在获取章节详情: ${planId}`);
  return request({
    url: `/tp/plan/${planId}`,
    method: 'get'
  }).then(response => {
    console.log(`获取章节${planId}详情成功:`, response);
    return response;
  }).catch(error => {
    console.error(`获取章节 ${planId} 详情失败:`, error);
    return Promise.reject(error);
  });
};

/**
 * 更新章节
 * @param {Object} planData 章节数据（必须包含id字段）
 * @returns {Promise<Object>} 更新结果
 */
export const updateLessonPlan = (planData) => {
  console.log('更新章节:', planData);
  return request({
    url: '/tp/plan',
    method: 'put',
    data: planData
  }).then(response => {
    console.log('更新章节成功:', response);
    return response;
  }).catch(error => {
    console.error('更新章节失败:', error);
    return Promise.reject(error);
  });
};

/**
 * 获取模块内容详细信息
 * @param {string|number} contentId 内容ID
 * @returns {Promise<Object>} 模块内容详情数据
 */
export const getModuleContentById = (contentId) => {
  console.log(`正在获取模块内容详情: ${contentId}`);
  return request({
    url: `/tp/content/${contentId}`,
    method: 'get'
  }).then(response => {
    console.log(`获取模块内容${contentId}详情成功:`, response);
    return response;
  }).catch(error => {
    console.error(`获取模块内容 ${contentId} 详情失败:`, error);
    return Promise.reject(error);
  });
};

/**
 * 新增模块内容
 * @param {Object} contentData 模块内容数据
 * @param {string} contentData.createBy 创建者
 * @param {string} contentData.createTime 创建时间
 * @param {string} contentData.updateBy 更新者
 * @param {string} contentData.updateTime 更新时间
 * @param {string} contentData.remark 备注
 * @param {Object} contentData.params 其他参数
 * @param {number} contentData.id 内容ID（新建时为0）
 * @param {number} contentData.moduleId 模块ID
 * @param {string} contentData.content 内容
 * @param {string} contentData.fileUrl 文件URL
 * @returns {Promise<Object>} 创建结果
 */
export const createModuleContent = (contentData) => {
  console.log('正在创建模块内容:', contentData);
  console.log('原始数据JSON:', JSON.stringify(contentData, null, 2));

  // 构建请求数据，过滤掉空值（但保留数字0）
  const cleanData = {};
  Object.keys(contentData).forEach(key => {
    const value = contentData[key];
    // 保留数字0，只过滤null、undefined和空字符串
    if (value !== null && value !== undefined && value !== '') {
      cleanData[key] = value;
    } else if (typeof value === 'number' && value === 0) {
      // 特别处理数字0，这是有效值
      cleanData[key] = value;
    }
  });

  console.log('清理后的数据JSON:', JSON.stringify(cleanData, null, 2));

  return request({
    url: '/tp/content',
    method: 'post',
    data: cleanData,
    headers: {
      'Content-Type': 'application/json'
    }
  }).then(response => {
    console.log('创建模块内容HTTP响应成功:', response);
    console.log('响应类型:', typeof response);
    console.log('响应JSON:', JSON.stringify(response, null, 2));
    return response;
  }).catch(error => {
    console.error('创建模块内容HTTP请求失败:', error);
    console.error('错误详情:', {
      message: error.message,
      status: error.status,
      data: error.data
    });
    return Promise.reject(error);
  });
};

/**
 * 更新模块内容
 * @param {Object} contentData 模块内容数据
 * @param {number} contentData.id 内容ID（必须大于0）
 * @param {number} contentData.moduleId 模块ID
 * @param {string} contentData.content 内容
 * @param {string} contentData.fileUrl 文件URL
 * @param {string} contentData.updateBy 更新者
 * @param {string} contentData.updateTime 更新时间
 * @returns {Promise<Object>} 更新结果
 */
export const updateModuleContent = (contentData) => {
  console.log('正在更新模块内容:', contentData);
  console.log('原始数据JSON:', JSON.stringify(contentData, null, 2));

  // 构建请求数据，过滤掉空值（但保留数字0）
  const cleanData = {};
  Object.keys(contentData).forEach(key => {
    const value = contentData[key];
    // 保留数字0，只过滤null、undefined和空字符串
    if (value !== null && value !== undefined && value !== '') {
      cleanData[key] = value;
    } else if (typeof value === 'number' && value === 0) {
      // 特别处理数字0，这是有效值
      cleanData[key] = value;
    }
  });

  console.log('清理后的数据JSON:', JSON.stringify(cleanData, null, 2));

  return request({
    url: '/tp/content',
    method: 'put',
    data: cleanData,
    headers: {
      'Content-Type': 'application/json'
    }
  }).then(response => {
    console.log('更新模块内容HTTP响应成功:', response);
    console.log('响应类型:', typeof response);
    console.log('响应JSON:', JSON.stringify(response, null, 2));
    return response;
  }).catch(error => {
    console.error('更新模块内容HTTP请求失败:', error);
    console.error('错误详情:', {
      message: error.message,
      status: error.status,
      data: error.data
    });
    return Promise.reject(error);
  });
};

/**
 * 创建章节模块
 * @param {Object} moduleData 模块数据
 * @param {string} moduleData.title 模块标题
 * @param {number} moduleData.planId 教案ID
 * @param {number} moduleData.sort 排序号
 * @param {Object} moduleData.content 模块内容对象
 * @param {string} moduleData.createBy 创建者
 * @param {string} moduleData.updateBy 更新者
 * @param {string} moduleData.createTime 创建时间
 * @param {string} moduleData.updateTime 更新时间
 * @param {string} moduleData.remark 备注
 * @param {Object} moduleData.params 其他参数
 * @returns {Promise<Object>} 创建结果
 */
export const createModule = (moduleData) => {
  console.log('正在创建章节模块:', moduleData);
  console.log('原始模块数据JSON:', JSON.stringify(moduleData, null, 2));

  // 构建请求数据，过滤掉空值（但保留数字0）
  const cleanData = {};
  Object.keys(moduleData).forEach(key => {
    const value = moduleData[key];
    // 保留数字0，只过滤null、undefined和空字符串
    if (value !== null && value !== undefined && value !== '') {
      cleanData[key] = value;
    } else if (typeof value === 'number' && value === 0) {
      // 特别处理数字0，这是有效值
      cleanData[key] = value;
    }
  });

  console.log('清理后的模块数据JSON:', JSON.stringify(cleanData, null, 2));

  return request({
    url: '/tp/module',
    method: 'post',
    data: cleanData,
    headers: {
      'Content-Type': 'application/json'
    }
  }).then(response => {
    console.log('创建章节模块HTTP响应成功:', response);
    console.log('响应类型:', typeof response);
    console.log('响应JSON:', JSON.stringify(response, null, 2));
    return response;
  }).catch(error => {
    console.error('创建章节模块HTTP请求失败:', error);
    console.error('错误详情:', {
      message: error.message,
      status: error.status,
      data: error.data
    });
    return Promise.reject(error);
  });
};

/**
 * 获取当前用户信息
 * @returns {Object|null} 用户信息
 */
export const getCurrentUser = () => {
  try {
    const userStr = localStorage.getItem('user') || sessionStorage.getItem('user');
    if (userStr) {
      return JSON.parse(userStr);
    }

    // 如果没有存储的用户信息，返回默认用户
    return {
      id: 1,
      username: 'teacher',
      name: '教师用户'
    };
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return {
      id: 1,
      username: 'teacher',
      name: '教师用户'
    };
  }
};
