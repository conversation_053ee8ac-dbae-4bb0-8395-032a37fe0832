<template>
  <div class="courses-panel">
    <!-- 课程筛选标签 -->
    <div class="filter-tabs">
      <div 
        class="filter-tab" 
        :class="{ active: courseFilter === 'all' }" 
        @click="courseFilter = 'all'"
      >
        全部课程
      </div>
      <div 
        class="filter-tab" 
        :class="{ active: courseFilter === 'ongoing' }" 
        @click="courseFilter = 'ongoing'"
      >
        进行中
      </div>
      <div 
        class="filter-tab" 
        :class="{ active: courseFilter === 'completed' }" 
        @click="courseFilter = 'completed'"
      >
        已完成
      </div>
    </div>
    
    <!-- 课程列表 -->
    <div v-if="filteredCourses.length > 0" class="courses-grid">
      <div 
        v-for="course in filteredCourses" 
        :key="course.id"
        :class="['course-card', `course-card-${course.type}`]"
        @click="enterCourse(course.id)"
      >
        <div class="course-header">
          <h3 class="course-title">{{ course.title }}</h3>
          <div class="course-teacher">{{ course.teacher }}</div>
        </div>
        <div class="course-stats">
          <div class="stat-item">
            <span class="stat-label">学习进度</span>
            <span class="stat-value">{{ course.progress }}%</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">总课时</span>
            <span class="stat-value">{{ course.totalHours }}学时</span>
          </div>
        </div>
        <div class="progress-section">
          <div class="progress-label">进度: {{ course.progress }}%</div>
          <div class="progress-bar">
            <div class="progress-fill" :style="`width: ${course.progress}%`"></div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 空状态 -->
    <div v-else class="empty-state">
      <div class="empty-icon">📚</div>
      <h3 class="empty-title">暂无课程</h3>
      <p class="empty-description">您还没有注册任何课程，请联系老师或管理员添加课程</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

// 课程筛选状态
const courseFilter = ref('ongoing');

// 学生课程数据
const courses = ref([
  {
    id: 1,
    title: '土木工程概论',
    teacher: '张教授',
    progress: 75,
    totalHours: 48,
    type: 'blue',
    status: 'ongoing'
  },
  {
    id: 2,
    title: '有限元分析',
    teacher: '李教授',
    progress: 60,
    totalHours: 36,
    type: 'green',
    status: 'ongoing'
  },
  {
    id: 3,
    title: '结构力学',
    teacher: '王教授',
    progress: 100,
    totalHours: 64,
    type: 'blue',
    status: 'completed'
  },
  {
    id: 4,
    title: '建筑材料学',
    teacher: '赵教授',
    progress: 45,
    totalHours: 32,
    type: 'green',
    status: 'ongoing'
  }
]);

// 筛选后的课程
const filteredCourses = computed(() => {
  if (courseFilter.value === 'all') {
    return courses.value;
  }
  return courses.value.filter(course => course.status === courseFilter.value);
});

// 课程相关方法
const enterCourse = (courseId) => {
  console.log('进入课程:', courseId);
  // TODO: 跳转到课程详情页
};
</script>

<style scoped>
/* 课程面板样式 */
.courses-panel {
  flex: 1;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 筛选标签样式 */
.filter-tabs {
  display: flex;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  padding: 0;
  margin-bottom: 1.5rem;
}

.filter-tab {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;
  font-weight: 500;
}

.filter-tab:hover {
  color: var(--primary-color);
  background-color: rgba(66, 153, 225, 0.05);
}

.filter-tab.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
  background-color: rgba(66, 153, 225, 0.1);
}

/* 课程网格样式 */
.courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
  overflow-y: auto;
}

.course-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.course-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.course-card-blue {
  border-left: 4px solid #3B82F6;
}

.course-card-green {
  border-left: 4px solid #10B981;
}

.course-header {
  margin-bottom: 1rem;
}

.course-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.course-teacher {
  font-size: 0.875rem;
  color: #6b7280;
}

.course-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.stat-label {
  font-size: 0.75rem;
  color: #9ca3af;
  margin-bottom: 0.25rem;
}

.stat-value {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
}

.progress-section {
  margin-top: 1rem;
}

.progress-label {
  font-size: 0.875rem;
  color: #374151;
  margin-bottom: 0.5rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  margin-top: 1rem;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  opacity: 0.6;
}

.empty-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 1rem 0;
}

.empty-description {
  font-size: 1rem;
  color: var(--text-color-secondary);
  margin: 0 0 2rem 0;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .courses-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  .courses-grid {
    grid-template-columns: 1fr;
  }
}
</style>
