<template>
  <div class="lesson-assistant-panel">
    <!-- 助手模块网格 -->
    <div class="assistant-modules-grid">
      <div class="module-card lesson-design-card" @click="handleLessonDesign">
        <div class="module-header">
          <div class="module-icon lesson-design-icon">
            <i class="icon-document"></i>
          </div>
          <div class="module-badge">智能推荐</div>
        </div>
        <div class="module-content">
          <h3 class="module-title">教案设计</h3>
          <p class="module-desc">智能教案生成和编辑工具，支持多种教学模式</p>
          <div class="module-stats">
            <div class="stat-item">
              <span class="stat-label">模板</span>
              <span class="stat-value">50+</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">已创建</span>
              <span class="stat-value">12</span>
            </div>
          </div>
        </div>
        <div class="module-footer">
          <button class="module-btn btn-blue">开始设计</button>
        </div>
      </div>
      
      <div class="module-card my-lessons-card" @click="handleMyLessons">
        <div class="module-header">
          <div class="module-icon my-lessons-icon">
            <i class="icon-folder"></i>
          </div>
          <div class="module-badge recent">最近使用</div>
        </div>
        <div class="module-content">
          <h3 class="module-title">我的教案</h3>
          <p class="module-desc">查看和管理已创建的教案，支持版本管理</p>
          <div class="module-stats">
            <div class="stat-item">
              <span class="stat-label">总数</span>
              <span class="stat-value">12</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">本周新增</span>
              <span class="stat-value">3</span>
            </div>
          </div>
        </div>
        <div class="module-footer">
          <button class="module-btn btn-purple">查看教案</button>
        </div>
      </div>
      
      <div class="module-card courseware-card" @click="handleCourseware">
        <div class="module-header">
          <div class="module-icon courseware-icon">
            <i class="icon-presentation"></i>
          </div>
          <div class="module-badge">热门功能</div>
        </div>
        <div class="module-content">
          <h3 class="module-title">课件制作</h3>
          <p class="module-desc">多媒体课件制作和管理，支持PPT一键生成</p>
          <div class="module-stats">
            <div class="stat-item">
              <span class="stat-label">课件数</span>
              <span class="stat-value">8</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">模板</span>
              <span class="stat-value">30+</span>
            </div>
          </div>
        </div>
        <div class="module-footer">
          <button class="module-btn btn-green">制作课件</button>
        </div>
      </div>
      
      <div class="module-card question-bank-card" @click="handleQuestionBank">
        <div class="module-header">
          <div class="module-icon question-bank-icon">
            <i class="icon-quiz"></i>
          </div>
          <div class="module-badge">AI生成</div>
        </div>
        <div class="module-content">
          <h3 class="module-title">习题库</h3>
          <p class="module-desc">智能习题库存和组卷，AI自动生成试题</p>
          <div class="module-stats">
            <div class="stat-item">
              <span class="stat-label">题目数</span>
              <span class="stat-value">256</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">试卷</span>
              <span class="stat-value">5</span>
            </div>
          </div>
        </div>
        <div class="module-footer">
          <button class="module-btn btn-orange">题库管理</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 教学助手模块处理方法
const handleLessonDesign = () => {
  console.log('开始教案设计');
  // TODO: 跳转到教案设计页面
};

const handleMyLessons = () => {
  console.log('查看我的教案');
  // TODO: 跳转到教案列表页面
};

const handleCourseware = () => {
  console.log('制作课件');
  // TODO: 跳转到课件制作页面
};

const handleQuestionBank = () => {
  console.log('题库管理');
  // TODO: 跳转到习题库页面
};
</script>

<style scoped>
/* 教学助手面板样式 */
.lesson-assistant-panel {
  flex: 1;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 教学助手模块网格样式 */
.assistant-modules-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.module-card {
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: 1px solid #f0f0f0;
  overflow: hidden;
  min-height: 280px;
}

.module-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  border-color: #d1d5db;
}

.module-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 1.5rem 1rem;
  position: relative;
}

.module-icon {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.lesson-design-icon {
  background: linear-gradient(135deg, #4F9CF9 0%, #3B82F6 100%);
}

.my-lessons-icon {
  background: linear-gradient(135deg, #A855F7 0%, #8B5CF6 100%);
}

.courseware-icon {
  background: linear-gradient(135deg, #10B981 0%, #059669 100%);
}

.question-bank-icon {
  background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
}

.module-icon i {
  font-size: 24px;
  color: white;
}

.icon-document::before {
  content: "📄";
  font-style: normal;
}

.icon-folder::before {
  content: "📁";
  font-style: normal;
}

.icon-presentation::before {
  content: "📊";
  font-style: normal;
}

.icon-quiz::before {
  content: "🎯";
  font-style: normal;
}

.module-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  background: #f3f4f6;
  color: #6b7280;
}

.module-badge.recent {
  background: #fef3c7;
  color: #d97706;
}

.module-content {
  flex: 1;
  padding: 0 1.5rem 1rem;
  text-align: left;
}

.module-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.module-desc {
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.5;
  margin: 0 0 1rem 0;
}

.module-stats {
  display: flex;
  gap: 1.5rem;
  margin-top: 1rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.stat-label {
  font-size: 0.75rem;
  color: #9ca3af;
  margin-bottom: 0.25rem;
  font-weight: 500;
}

.stat-value {
  font-size: 1.125rem;
  font-weight: 700;
  color: #1f2937;
}

.module-footer {
  padding: 1rem 1.5rem 1.5rem;
  border-top: 1px solid #f3f4f6;
  background: rgba(249, 250, 251, 0.5);
}

.module-btn {
  width: 100%;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-blue {
  background: #3B82F6;
  color: white;
}

.btn-blue:hover {
  background: #2563EB;
}

.btn-purple {
  background: #8B5CF6;
  color: white;
}

.btn-purple:hover {
  background: #7C3AED;
}

.btn-green {
  background: #10B981;
  color: white;
}

.btn-green:hover {
  background: #059669;
}

.btn-orange {
  background: #F59E0B;
  color: white;
}

.btn-orange:hover {
  background: #D97706;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .assistant-modules-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
  }
}

@media (max-width: 768px) {
  .assistant-modules-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .module-card {
    min-height: 240px;
  }
  
  .module-header {
    padding: 1.25rem 1.25rem 0.75rem;
  }
  
  .module-content {
    padding: 0 1.25rem 0.75rem;
  }
  
  .module-footer {
    padding: 0.75rem 1.25rem 1.25rem;
  }
  
  .module-icon {
    width: 50px;
    height: 50px;
  }
  
  .module-icon i {
    font-size: 20px;
  }
}
</style>
