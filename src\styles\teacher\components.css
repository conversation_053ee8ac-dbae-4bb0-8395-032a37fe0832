/* 教师端组件样式 */

/* 导入变量和通用样式 */
@import './variables.css';
@import './common.css';

/* 按钮组件样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--button-padding-y) var(--button-padding-x);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  border-radius: var(--border-radius-md);
  border: 1px solid transparent;
  cursor: pointer;
  transition: all var(--transition-base);
  text-decoration: none;
  white-space: nowrap;
  user-select: none;
  min-height: var(--button-height);
  font-family: var(--font-family-sans);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn-sm {
  padding: var(--button-padding-y-sm) var(--button-padding-x-sm);
  font-size: var(--font-size-xs);
  min-height: var(--button-height-sm);
}

.btn-lg {
  padding: var(--button-padding-y-lg) var(--button-padding-x-lg);
  font-size: var(--font-size-base);
  min-height: var(--button-height-lg);
}

/* 按钮变体 */
.btn-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-color-dark) 100%);
  color: var(--text-color-inverse);
  box-shadow: 0 2px 4px rgba(66, 153, 225, 0.3);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(66, 153, 225, 0.4);
}

.btn-secondary {
  background-color: var(--gray-100);
  color: var(--text-color);
  border-color: var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--gray-200);
  border-color: var(--border-color-dark);
  transform: translateY(-1px);
}

.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline:hover:not(:disabled) {
  background-color: var(--primary-color-lightest);
  transform: translateY(-1px);
}

.btn-success {
  background: linear-gradient(135deg, var(--success-color) 0%, var(--success-color-dark) 100%);
  color: var(--text-color-inverse);
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

.btn-success:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.4);
}

.btn-warning {
  background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-color-dark) 100%);
  color: var(--text-color-inverse);
  box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
}

.btn-warning:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(245, 158, 11, 0.4);
}

.btn-danger {
  background: linear-gradient(135deg, var(--danger-color) 0%, var(--danger-color-dark) 100%);
  color: var(--text-color-inverse);
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
}

.btn-danger:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(239, 68, 68, 0.4);
}

/* 按钮图标 */
.btn-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: var(--spacing-sm);
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  flex-shrink: 0;
}

.btn-sm .btn-icon {
  width: 14px;
  height: 14px;
}

.btn-lg .btn-icon {
  width: 18px;
  height: 18px;
}

/* 卡片组件样式 */
.card {
  background-color: var(--background-color);
  border-radius: var(--card-border-radius);
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color-light);
  overflow: hidden;
  transition: all var(--transition-base);
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--card-padding);
  border-bottom: 1px solid var(--border-color-light);
  background-color: var(--background-color-secondary);
}

.card-body {
  padding: var(--card-padding);
}

.card-footer {
  padding: var(--card-padding);
  border-top: 1px solid var(--border-color-light);
  background-color: var(--background-color-secondary);
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-color);
  margin: 0 0 var(--spacing-sm) 0;
}

.card-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-color-secondary);
  margin: 0 0 var(--spacing-md) 0;
}

.card-text {
  color: var(--text-color);
  line-height: var(--line-height-relaxed);
}

/* 表单组件样式 */
.form-group {
  margin-bottom: var(--spacing-2xl);
}

.form-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-color);
  margin-bottom: var(--spacing-sm);
}

.form-label.required::after {
  content: ' *';
  color: var(--danger-color);
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: var(--input-padding-y) var(--input-padding-x);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  color: var(--text-color);
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  transition: all var(--transition-base);
  min-height: var(--input-height);
  font-family: var(--font-family-sans);
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: var(--border-color-focus);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input:disabled,
.form-textarea:disabled,
.form-select:disabled {
  background-color: var(--gray-50);
  color: var(--text-color-muted);
  cursor: not-allowed;
}

.form-input.error,
.form-textarea.error,
.form-select.error {
  border-color: var(--danger-color);
}

.form-input.error:focus,
.form-textarea.error:focus,
.form-select.error:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-select {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%236b7280'%3E%3Cpath fill-rule='evenodd' d='M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z' clip-rule='evenodd' /%3E%3C/svg%3E");
  background-position: right var(--spacing-md) center;
  background-size: 1rem;
  background-repeat: no-repeat;
  padding-right: 2.5rem;
  appearance: none;
}

.form-error {
  display: block;
  font-size: var(--font-size-xs);
  color: var(--danger-color);
  margin-top: var(--spacing-xs);
}

.form-help {
  display: block;
  font-size: var(--font-size-xs);
  color: var(--text-color-secondary);
  margin-top: var(--spacing-xs);
}

/* 复选框和单选框 */
.form-checkbox,
.form-radio {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  font-size: var(--font-size-sm);
  color: var(--text-color);
}

.form-checkbox input,
.form-radio input {
  margin-right: var(--spacing-sm);
  cursor: pointer;
}

/* 加载状态组件 */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--gray-200);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-dots {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.loading-dots .dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--primary-color);
  animation: dotPulse 1.4s ease-in-out infinite both;
}

.loading-dots .dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dots .dot:nth-child(2) { animation-delay: -0.16s; }
.loading-dots .dot:nth-child(3) { animation-delay: 0s; }

@keyframes dotPulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 徽章组件 */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  border-radius: var(--border-radius-full);
  white-space: nowrap;
}

.badge-primary {
  background-color: var(--primary-color-lightest);
  color: var(--primary-color-dark);
}

.badge-secondary {
  background-color: var(--gray-100);
  color: var(--gray-700);
}

.badge-success {
  background-color: #d1fae5;
  color: var(--success-color-dark);
}

.badge-warning {
  background-color: #fef3c7;
  color: var(--warning-color-dark);
}

.badge-danger {
  background-color: #fee2e2;
  color: var(--danger-color-dark);
}

/* 进度条组件 */
.progress {
  width: 100%;
  height: 8px;
  background-color: var(--gray-200);
  border-radius: var(--border-radius-full);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-color-dark) 100%);
  border-radius: var(--border-radius-full);
  transition: width var(--transition-slow);
}

.progress-sm {
  height: 4px;
}

.progress-lg {
  height: 12px;
}

/* 分隔线组件 */
.divider {
  border: none;
  height: 1px;
  background-color: var(--border-color);
  margin: var(--spacing-2xl) 0;
}

.divider-vertical {
  width: 1px;
  height: auto;
  background-color: var(--border-color);
  margin: 0 var(--spacing-2xl);
}

/* 工具提示组件 */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip-content {
  position: absolute;
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--gray-800);
  color: var(--text-color-inverse);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius);
  font-size: var(--font-size-xs);
  white-space: nowrap;
  z-index: var(--z-index-tooltip);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-base);
}

.tooltip:hover .tooltip-content {
  opacity: 1;
  visibility: visible;
}

.tooltip-content::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: var(--gray-800);
}
