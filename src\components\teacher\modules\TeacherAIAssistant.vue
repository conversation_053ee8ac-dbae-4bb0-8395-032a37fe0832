<template>
  <div class="ai-assistant-panel">
    <!-- AI助手模块网格 -->
    <div class="ai-modules-grid">
      <div class="module-card ai-lesson-card" @click="handleAILessonGeneration">
        <div class="module-header">
          <div class="module-icon ai-lesson-icon">
            <i class="icon-ai-lesson"></i>
          </div>
        </div>
        <div class="module-content">
          <h3 class="module-title">AI生成教案</h3>
          <p class="module-desc">智能生成个性化教案，支持多种教学模板和课程类型</p>
        </div>
        <div class="module-footer">
          <button class="module-btn btn-blue">开始生成</button>
        </div>
      </div>
      
      <div class="module-card ai-ppt-card" @click="handleAIPPTGeneration">
        <div class="module-header">
          <div class="module-icon ai-ppt-icon">
            <i class="icon-ai-ppt"></i>
          </div>
        </div>
        <div class="module-content">
          <h3 class="module-title">AI生成PPT</h3>
          <p class="module-desc">自动生成精美课件，支持多种设计风格和模板选择</p>
        </div>
        <div class="module-footer">
          <button class="module-btn btn-green">开始制作</button>
        </div>
      </div>
      
      <div class="module-card ai-chat-card" @click="handleAIChatAssistant">
        <div class="module-header">
          <div class="module-icon ai-chat-icon">
            <i class="icon-ai-chat"></i>
          </div>
        </div>
        <div class="module-content">
          <h3 class="module-title">AI智能问答助手</h3>
          <p class="module-desc">专业教学问题解答，提供教学建议和资源推荐</p>
        </div>
        <div class="module-footer">
          <button class="module-btn btn-orange">开始对话</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// AI助手模块处理方法
const handleAILessonGeneration = () => {
  console.log('AI生成教案');
  // TODO: 跳转到AI教案生成页面
};

const handleAIPPTGeneration = () => {
  console.log('AI生成PPT');
  // TODO: 跳转到AI PPT生成页面
};

const handleAIChatAssistant = () => {
  console.log('AI智能问答');
  // TODO: 跳转到AI问答助手页面
};
</script>

<style scoped>
/* AI助手面板样式 */
.ai-assistant-panel {
  flex: 1;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* AI助手模块网格样式 */
.ai-modules-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.module-card {
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: 1px solid #f0f0f0;
  overflow: hidden;
  min-height: 280px;
}

.module-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  border-color: #d1d5db;
}

.module-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 1.5rem 1rem;
  position: relative;
}

.module-icon {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* AI助手模块图标颜色 */
.ai-lesson-icon {
  background: linear-gradient(135deg, #4F9CF9 0%, #3B82F6 100%);
}

.ai-ppt-icon {
  background: linear-gradient(135deg, #10B981 0%, #059669 100%);
}

.ai-chat-icon {
  background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
}

.module-icon i {
  font-size: 24px;
  color: white;
}

/* AI助手图标样式 */
.icon-ai-lesson::before {
  content: "📝";
  font-style: normal;
}

.icon-ai-ppt::before {
  content: "📈";
  font-style: normal;
}

.icon-ai-chat::before {
  content: "🤖";
  font-style: normal;
}

.module-content {
  flex: 1;
  padding: 0 1.5rem 1rem;
  text-align: left;
}

.module-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.module-desc {
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.5;
  margin: 0 0 1rem 0;
}

.module-footer {
  padding: 1rem 1.5rem 1.5rem;
  border-top: 1px solid #f3f4f6;
  background: rgba(249, 250, 251, 0.5);
}

.module-btn {
  width: 100%;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-blue {
  background: #3B82F6;
  color: white;
}

.btn-blue:hover {
  background: #2563EB;
}

.btn-green {
  background: #10B981;
  color: white;
}

.btn-green:hover {
  background: #059669;
}

.btn-orange {
  background: #F59E0B;
  color: white;
}

.btn-orange:hover {
  background: #D97706;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .ai-modules-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
  }
}

@media (max-width: 768px) {
  .ai-modules-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .module-card {
    min-height: 240px;
  }
  
  .module-header {
    padding: 1.25rem 1.25rem 0.75rem;
  }
  
  .module-content {
    padding: 0 1.25rem 0.75rem;
  }
  
  .module-footer {
    padding: 0.75rem 1.25rem 1.25rem;
  }
  
  .module-icon {
    width: 50px;
    height: 50px;
  }
  
  .module-icon i {
    font-size: 20px;
  }
}
</style>
