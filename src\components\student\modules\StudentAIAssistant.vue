<template>
  <div class="ai-assistant-panel">
    <div class="ai-modules-grid">
      <div class="module-card ai-chat-card" @click="handleAIChatAssistant">
        <div class="module-header">
          <div class="module-icon ai-chat-icon">
            <i class="icon-ai-chat"></i>
          </div>
        </div>
        <div class="module-content">
          <h3 class="module-title">AI学习助手</h3>
          <p class="module-desc">智能问答助手，随时为您解答学习中的疑问</p>
        </div>
        <div class="module-footer">
          <button class="module-btn btn-blue">开始对话</button>
        </div>
      </div>

      <div class="module-card ai-tutor-card" @click="handleAITutor">
        <div class="module-header">
          <div class="module-icon ai-tutor-icon">
            <i class="icon-ai-tutor"></i>
          </div>
        </div>
        <div class="module-content">
          <h3 class="module-title">AI学习指导</h3>
          <p class="module-desc">个性化学习建议和学习路径规划</p>
        </div>
        <div class="module-footer">
          <button class="module-btn btn-green">获取指导</button>
        </div>
      </div>

      <div class="module-card ai-practice-card" @click="handleAIPractice">
        <div class="module-header">
          <div class="module-icon ai-practice-icon">
            <i class="icon-ai-practice"></i>
          </div>
        </div>
        <div class="module-content">
          <h3 class="module-title">AI练习生成</h3>
          <p class="module-desc">根据学习进度智能生成练习题目</p>
        </div>
        <div class="module-footer">
          <button class="module-btn btn-orange">开始练习</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// AI助手相关方法
const handleAIChatAssistant = () => {
  console.log('AI学习助手');
  // TODO: 跳转到AI对话页面
};

const handleAITutor = () => {
  console.log('AI学习指导');
  // TODO: 跳转到AI学习指导页面
};

const handleAIPractice = () => {
  console.log('AI练习生成');
  // TODO: 跳转到AI练习页面
};
</script>

<style scoped>
/* AI助手面板样式 */
.ai-assistant-panel {
  flex: 1;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* AI助手模块样式 */
.ai-modules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.module-card {
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: 1px solid #f0f0f0;
  overflow: hidden;
  min-height: 280px;
}

.module-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  border-color: #d1d5db;
}

.module-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1.5rem 1.5rem 1rem;
  position: relative;
}

.module-icon {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.ai-chat-icon {
  background: linear-gradient(135deg, #4F9CF9 0%, #3B82F6 100%);
}

.ai-tutor-icon {
  background: linear-gradient(135deg, #10B981 0%, #059669 100%);
}

.ai-practice-icon {
  background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
}

.module-icon i {
  font-size: 24px;
  color: white;
}

.icon-ai-chat::before {
  content: "🤖";
  font-style: normal;
}

.icon-ai-tutor::before {
  content: "🎓";
  font-style: normal;
}

.icon-ai-practice::before {
  content: "📝";
  font-style: normal;
}

.module-content {
  flex: 1;
  padding: 0 1.5rem 1rem;
  text-align: center;
}

.module-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.module-desc {
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.5;
  margin: 0;
}

.module-footer {
  padding: 1rem 1.5rem 1.5rem;
  border-top: 1px solid #f3f4f6;
  background: rgba(249, 250, 251, 0.5);
}

.module-btn {
  width: 100%;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-blue {
  background: #3B82F6;
  color: white;
}

.btn-blue:hover {
  background: #2563EB;
}

.btn-green {
  background: #10B981;
  color: white;
}

.btn-green:hover {
  background: #059669;
}

.btn-orange {
  background: #F59E0B;
  color: white;
}

.btn-orange:hover {
  background: #D97706;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .ai-modules-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  .ai-modules-grid {
    grid-template-columns: 1fr;
  }
}
</style>
