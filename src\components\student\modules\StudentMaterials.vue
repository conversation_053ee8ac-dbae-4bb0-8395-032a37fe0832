<template>
  <div class="materials-panel">
    <div class="materials-grid">
      <div class="material-category-card course-materials-card" @click="viewCourseMaterials">
        <div class="category-icon">📁</div>
        <div class="category-info">
          <h3 class="category-title">课程资料</h3>
          <p class="category-count">{{ courseMaterials.count }}个文件</p>
        </div>
      </div>

      <div class="material-category-card shared-resources-card" @click="viewSharedResources">
        <div class="category-icon">📤</div>
        <div class="category-info">
          <h3 class="category-title">教师共享</h3>
          <p class="category-count">{{ sharedResources.count }}个文件</p>
        </div>
      </div>

      <div class="material-category-card video-resources-card" @click="viewVideoResources">
        <div class="category-icon">🎥</div>
        <div class="category-info">
          <h3 class="category-title">视频资源</h3>
          <p class="category-count">{{ videoResources.count }}个文件</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

// 学习资料数据
const courseMaterials = ref({
  count: 25,
  files: []
});

const sharedResources = ref({
  count: 18,
  files: []
});

const videoResources = ref({
  count: 12,
  files: []
});

// 学习资料相关方法
const viewCourseMaterials = () => {
  console.log('查看课程资料');
  // TODO: 跳转到课程资料列表
};

const viewSharedResources = () => {
  console.log('查看教师共享资源');
  // TODO: 跳转到共享资源列表
};

const viewVideoResources = () => {
  console.log('查看视频资源');
  // TODO: 跳转到视频资源列表
};
</script>

<style scoped>
/* 学习资料面板样式 */
.materials-panel {
  flex: 1;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 学习资料网格样式 */
.materials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

.material-category-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
  text-align: center;
}

.material-category-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.category-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.category-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.category-count {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .materials-grid {
    grid-template-columns: 1fr;
  }
}
</style>
