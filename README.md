# 知识图谱网站前端

## 项目简介

知识图谱网站是一个可视化学习和知识管理平台，帮助用户构建、分享和学习知识体系。通过图形化的方式展示知识点之间的关联，使复杂的知识结构变得直观易懂。

## 技术栈

- Vue 3
- Vue Router
- Axios
- Relation Graph (知识图谱可视化库)
- CSS变量和自定义属性
- 响应式设计

## 功能特点

- 知识图谱可视化
- 知识大纲树状展示
- 个性化学习路径推荐
- 响应式设计，支持多种设备
- 暗色模式支持

## 项目结构

```
knowledge-graph-frontend/
├── public/             # 静态资源
├── src/                # 源代码
│   ├── api/            # API接口
│   ├── assets/         # 资源文件（样式、图片等）
│   ├── components/     # 组件
│   │   ├── common/     # 通用组件
│   │   ├── graph/      # 图谱相关组件
│   │   ├── home/       # 首页相关组件
│   │   ├── my/         # 个人中心组件
│   │   └── outline/    # 大纲相关组件
│   ├── layouts/        # 布局组件
│   ├── pages/          # 页面组件
│   ├── router/         # 路由配置
│   ├── App.vue         # 根组件
│   └── main.js         # 入口文件
├── index.html          # HTML模板
├── package.json        # 项目依赖
└── vite.config.ts      # Vite配置
```

## 开发指南

### 环境要求

- Node.js >= 14.0.0
- npm >= 6.0.0 或 yarn >= 1.22.0

### 安装依赖

```bash
npm install
# 或
yarn
```

### 开发服务器

```bash
npm run dev
# 或
yarn dev
```

### 构建生产版本

```bash
npm run build
# 或
yarn build
```

## 项目开发历程

### 基础架构搭建
- 创建了项目基础结构和目录
- 实现了首页布局和组件
- 设置了路由系统
- 配置了API服务
- 创建了全局样式和主题

### 工具集成
- 集成了MCP相关依赖包
- 添加了stagewise工具栏，提供基于浏览器的AI辅助编辑功能
- 配置了Context7 MCP、Playwright MCP和Filesystem MCP工具

### 大纲功能开发
- 创建了课程选择组件和课程大纲树形结构
- 实现了课程切换功能
- 优化了大纲树视图UI，提升视觉体验和交互效果
- 修复了展开/折叠全部节点功能
- 将页面改造为左侧课程列表、右侧知识点内容的布局
- 简化了界面，移除了知识点详情部分
- 优化了头部区域，使知识列表有更多显示空间

### 知识图谱功能开发
- 创建了知识图谱页面组件
- 实现了知识图谱的交互式可视化展示
- 添加了节点详情面板和悬浮提示
- 实现了图谱控制功能（缩放、重置视图等）
- 添加了学习路径显示和学习进度跟踪功能
- 创建了节点编辑对话框组件
- 实现了每个课程对应不同知识图谱的功能
- 添加了中心布局和力导向布局切换功能
- 修复了点击知识图谱节点后知识点标题不显示的问题
- 重新设计了节点详情面板，使其更符合教师审美偏好
- 优化了保存和取消按钮样式
- 添加了"相关资料"区域，增强知识点的扩展资源管理功能

### 用户认证功能
- 创建了用户认证相关的API服务模块
- 开发了登录页面，支持记住用户名功能
- 开发了用户注册页面，包含完整的表单验证
- 开发了找回密码和重置密码页面
- 添加了教师权限管理员账号（用户名和密码均为"admin"）

### UI/UX优化
- 添加了暗色模式（夜间模式）功能
- 为网站添加了科技感和学术风格的UI设计
- 优化了首页演示知识图谱，修复了文字偏移和节点样式问题
- 将演示知识图谱修改为真实的土木工程概论知识图谱
- 实现了进入网站时自动滚动到首页顶部的功能
- 在大纲页面中隐藏页脚部分，提供更多内容显示空间
- 创建了全面的响应式设计系统，实现移动优先的设计理念
- 优化并统一了色彩系统，减少颜色种类，提高对比度

### 后端集成
- 修改API请求的后端服务器地址
- 创建了知识图谱数据库表结构SQL文件
- 设计了后端数据库数据与前端组件数据的映射关系
- 修复了API调用中的错误和TypeScript类型问题
- 将本地知识图谱项目连接到Gitee远程仓库

### 功能修复与优化
- 修复了API响应格式处理问题
- 解决了课程列表不显示的问题
- 修复了大纲模式显示错误
- 修复了AI搜索功能返回格式混乱的问题
- 修复了图片搜索功能，更改为使用百度图片搜索API
- 修复了页面跳转问题并为个人中心页面添加返回首页按钮
- 删除了不需要的分享功能相关文件和代码

## 主要实现功能

1. **知识图谱可视化**：
   - 基于relation-graph-vue3实现知识图谱的交互式可视化
   - 支持中心布局和力导向布局两种展示方式
   - 实现节点点击、悬停和线条点击等交互功能

2. **大纲树视图**：
   - 实现树形结构的知识点大纲展示
   - 支持节点展开/折叠功能
   - 优化UI，增强视觉层次和交互效果

3. **用户认证**：
   - 实现登录、注册、找回密码等功能
   - 支持用户会话管理和权限控制
   - 添加了教师权限管理员账号

4. **知识图谱编辑功能**：
   - 支持节点标题和内容的编辑
   - 实现图谱节点数据的实时更新和视觉同步
   - 集成AI搜索功能，一键获取知识点相关内容
   - 添加相关资料管理功能

5. **UI/UX增强**：
   - 实现暗色模式支持
   - 添加科技感和学术风格的UI设计
   - 实现响应式设计，适配不同设备
   - 优化用户交互体验和视觉反馈

## 技术亮点

- 使用Vue 3组合式API构建高性能组件
- 基于CSS变量实现主题切换和样式统一
- 实现复杂的知识图谱数据结构转换和同步
- 使用TypeScript提高代码类型安全性
- 集成多种MCP工具提升开发效率
- 实现响应式设计，确保在各种设备上的良好体验

---

## 🎓 教师备课助手模块

### 新增功能模块

本项目现已集成**教师备课助手**功能，为教育工作者提供全面的备课支持：

#### 🎯 核心功能

1. **教案生成模块** (`/teacher/lesson-generator`)
   - 🤖 AI智能对话生成教案
   - 📝 支持多种教学主题
   - 💡 提供示例主题快速开始
   - 📋 完整的教学流程设计

2. **创建教案模块** (`/teacher/lesson-create`)
   - 📄 结构化教案创建表单
   - ✅ 实时表单验证
   - 💾 支持草稿保存
   - 🎯 包含教学目标、重难点等完整字段

3. **教案列表模块** (`/teacher/lesson-list`)
   - 📚 卡片式教案展示
   - 🔍 强大的搜索和筛选功能
   - 📄 支持分页浏览
   - ⚡ 快速编辑、删除操作

4. **PPT生成模块** (`/teacher/ppt-generator`)
   - 🎨 AI驱动的PPT内容生成
   - 👀 实时幻灯片预览
   - 📊 多种设计模板
   - 💾 一键下载功能

5. **相关资料模块** (`/teacher/materials`)
   - 📁 支持多格式文件上传
   - 🏷️ 智能文件分类
   - 👁️ 在线文件预览
   - 📱 拖拽上传体验

#### 🎨 设计特色

- **现代化布局**：采用侧边栏导航 + 主内容区域的经典布局
- **响应式设计**：完美适配桌面、平板和手机设备
- **深色主题**：支持明暗主题无缝切换
- **流畅动画**：精心设计的过渡动画效果
- **无障碍支持**：遵循Web无障碍标准

#### 🛠️ 技术实现

- **Vue 3 Composition API**：现代化的组件开发方式
- **模块化架构**：高度可维护的代码结构
- **组件化设计**：可复用的UI组件库
- **响应式工具**：智能的屏幕适配方案
#### 🚀 快速访问

访问教师备课助手：`http://localhost:5173/teacher`

#### 🎯 使用说明

1. **教案生成**：通过AI对话快速生成教案内容
2. **手动创建**：使用结构化表单创建详细教案
3. **管理教案**：在列表页面查看、编辑和删除教案
4. **PPT制作**：AI辅助生成精美的教学PPT
5. **资料管理**：上传和管理各类教学资料

---

## 🤝 贡献指南

欢迎为项目贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情
