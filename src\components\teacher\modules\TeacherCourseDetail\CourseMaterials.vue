<template>
  <div class="course-materials">
    <!-- 页面标题 -->
    <div class="section-header">
      <h2 class="section-title">课程资料</h2>
      <div class="section-actions">
        <button class="btn btn-primary" @click="uploadMaterial">
          <i class="btn-icon upload-icon"></i>
          上传资料
        </button>
        <button class="btn btn-secondary" @click="createFolder">
          <i class="btn-icon folder-icon"></i>
          新建文件夹
        </button>
      </div>
    </div>

    <!-- 资料统计 -->
    <div class="materials-stats">
      <div class="stat-card">
        <div class="stat-number">{{ materialStats.totalFiles }}</div>
        <div class="stat-label">总文件数</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ formatFileSize(materialStats.totalSize) }}</div>
        <div class="stat-label">总大小</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ materialStats.totalDownloads }}</div>
        <div class="stat-label">下载次数</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ materialStats.recentUploads }}</div>
        <div class="stat-label">本周上传</div>
      </div>
    </div>

    <!-- 文件操作栏 -->
    <div class="file-toolbar">
      <div class="view-controls">
        <button
          :class="['view-btn', { active: viewMode === 'list' }]"
          @click="setViewMode('list')"
        >
          <i class="list-icon"></i>
          列表
        </button>
        <button
          :class="['view-btn', { active: viewMode === 'grid' }]"
          @click="setViewMode('grid')"
        >
          <i class="grid-icon"></i>
          网格
        </button>
      </div>

      <div class="file-actions">
        <select v-model="sortBy" class="sort-select" @change="sortMaterials">
          <option value="name">按名称排序</option>
          <option value="date">按日期排序</option>
          <option value="size">按大小排序</option>
          <option value="type">按类型排序</option>
        </select>

        <div class="search-box">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索文件..."
            class="search-input"
            @input="handleSearch"
          >
          <i class="search-icon"></i>
        </div>
      </div>
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <span
        v-for="(crumb, index) in breadcrumbs"
        :key="index"
        class="breadcrumb-item"
        @click="navigateToFolder(crumb.path)"
      >
        {{ crumb.name }}
        <i v-if="index < breadcrumbs.length - 1" class="breadcrumb-separator"></i>
      </span>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <div class="loading-spinner"></div>
      <p class="loading-text">正在加载资料列表...</p>
    </div>

    <!-- 资料列表 -->
    <div v-else-if="filteredMaterials.length > 0" :class="['materials-container', viewMode]">
      <div
        v-for="material in filteredMaterials"
        :key="material.id"
        :class="['material-item', material.type]"
        @click="handleMaterialClick(material)"
        @contextmenu.prevent="showContextMenu(material, $event)"
      >
        <div class="material-icon">
          <i :class="getFileIcon(material)"></i>
        </div>

        <div class="material-info">
          <h3 class="material-name">{{ material.name }}</h3>
          <div class="material-meta">
            <span class="meta-item">
              <i class="size-icon"></i>
              {{ formatFileSize(material.size) }}
            </span>
            <span class="meta-item">
              <i class="date-icon"></i>
              {{ formatDate(material.uploadDate) }}
            </span>
            <span class="meta-item">
              <i class="download-icon"></i>
              {{ material.downloadCount }}次下载
            </span>
          </div>
          <p v-if="material.description" class="material-description">
            {{ material.description }}
          </p>
        </div>

        <div class="material-actions">
          <button class="action-btn" @click.stop="downloadMaterial(material)">
            <i class="download-icon"></i>
            下载
          </button>
          <button class="action-btn" @click.stop="shareMaterial(material)">
            <i class="share-icon"></i>
            分享
          </button>
          <button class="action-btn" @click.stop="editMaterial(material)">
            <i class="edit-icon"></i>
            编辑
          </button>
          <button class="action-btn danger" @click.stop="deleteMaterial(material.id)">
            <i class="delete-icon"></i>
            删除
          </button>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <div class="empty-icon">📁</div>
      <h3 class="empty-title">暂无资料</h3>
      <p class="empty-description">开始上传您的第一个课程资料吧</p>
      <button class="btn btn-primary" @click="uploadMaterial">
        <i class="upload-icon"></i>
        上传资料
      </button>
    </div>

    <!-- 右键菜单 -->
    <div
      v-if="contextMenu.visible"
      class="context-menu"
      :style="{ top: contextMenu.y + 'px', left: contextMenu.x + 'px' }"
      @click="hideContextMenu"
    >
      <div class="context-menu-item" @click="downloadMaterial(contextMenu.material)">
        <i class="download-icon"></i>
        下载
      </div>
      <div class="context-menu-item" @click="shareMaterial(contextMenu.material)">
        <i class="share-icon"></i>
        分享
      </div>
      <div class="context-menu-item" @click="renameMaterial(contextMenu.material)">
        <i class="edit-icon"></i>
        重命名
      </div>
      <div class="context-menu-divider"></div>
      <div class="context-menu-item danger" @click="deleteMaterial(contextMenu.material.id)">
        <i class="delete-icon"></i>
        删除
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';

// 定义props
const props = defineProps({
  courseId: {
    type: [String, Number],
    required: true
  }
});

// 定义emits
const emit = defineEmits(['refresh']);

// 响应式数据
const loading = ref(false);
const materials = ref([]);
const searchQuery = ref('');
const sortBy = ref('name');
const viewMode = ref('list');
const currentPath = ref('/');

// 资料统计
const materialStats = ref({
  totalFiles: 0,
  totalSize: 0,
  totalDownloads: 0,
  recentUploads: 0
});

// 面包屑导航
const breadcrumbs = ref([
  { name: '根目录', path: '/' }
]);

// 右键菜单
const contextMenu = ref({
  visible: false,
  x: 0,
  y: 0,
  material: null
});

// 计算过滤后的资料
const filteredMaterials = computed(() => {
  let filtered = materials.value;

  // 按搜索关键词过滤
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(material =>
      material.name.toLowerCase().includes(query) ||
      (material.description && material.description.toLowerCase().includes(query))
    );
  }

  // 排序
  filtered.sort((a, b) => {
    switch (sortBy.value) {
      case 'name':
        return a.name.localeCompare(b.name);
      case 'date':
        return new Date(b.uploadDate) - new Date(a.uploadDate);
      case 'size':
        return b.size - a.size;
      case 'type':
        return a.type.localeCompare(b.type);
      default:
        return 0;
    }
  });

  return filtered;
});

// 加载资料列表
const loadMaterials = async () => {
  loading.value = true;
  try {
    // TODO: 实际的API调用
    // const response = await getMaterialsList(props.courseId, currentPath.value);
    // materials.value = response.data;

    // 模拟土木工程结构力学课程资料数据
    materials.value = [
      {
        id: 1,
        name: '结构力学教学大纲.pdf',
        type: 'pdf',
        size: 2048576, // 2MB
        uploadDate: '2024-04-01',
        downloadCount: 45,
        description: '结构力学课程教学大纲，包含课程目标、教学内容和考核方式'
      },
      {
        id: 2,
        name: '第一章-绪论.pptx',
        type: 'pptx',
        size: 5242880, // 5MB
        uploadDate: '2024-04-02',
        downloadCount: 38,
        description: '第一章课件，介绍结构力学的基本概念和研究内容'
      },
      {
        id: 3,
        name: '静定梁计算例题.docx',
        type: 'docx',
        size: 1048576, // 1MB
        uploadDate: '2024-04-05',
        downloadCount: 52,
        description: '静定梁内力计算的典型例题和解题步骤'
      },
      {
        id: 4,
        name: '结构力学公式汇总.pdf',
        type: 'pdf',
        size: 512000, // 500KB
        uploadDate: '2024-04-08',
        downloadCount: 67,
        description: '结构力学常用公式汇总表，便于学生查阅'
      },
      {
        id: 5,
        name: '桁架分析软件',
        type: 'folder',
        size: 0,
        uploadDate: '2024-04-10',
        downloadCount: 0,
        description: '桁架结构分析相关软件和使用说明'
      },
      {
        id: 6,
        name: '期中考试试卷.pdf',
        type: 'pdf',
        size: 1536000, // 1.5MB
        uploadDate: '2024-04-12',
        downloadCount: 28,
        description: '2024年春季学期期中考试试卷及参考答案'
      }
    ];

    // 计算统计数据
    materialStats.value = {
      totalFiles: materials.value.filter(m => m.type !== 'folder').length,
      totalSize: materials.value.reduce((sum, m) => sum + m.size, 0),
      totalDownloads: materials.value.reduce((sum, m) => sum + m.downloadCount, 0),
      recentUploads: materials.value.filter(m => {
        const uploadDate = new Date(m.uploadDate);
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        return uploadDate >= weekAgo;
      }).length
    };
  } catch (error) {
    console.error('加载资料列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 格式化日期
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN');
};

// 获取文件图标
const getFileIcon = (material) => {
  const iconMap = {
    'pdf': 'pdf-icon',
    'docx': 'word-icon',
    'pptx': 'ppt-icon',
    'xlsx': 'excel-icon',
    'folder': 'folder-icon',
    'image': 'image-icon',
    'video': 'video-icon',
    'audio': 'audio-icon'
  };
  return iconMap[material.type] || 'file-icon';
};

// 设置视图模式
const setViewMode = (mode) => {
  viewMode.value = mode;
};

// 排序资料
const sortMaterials = () => {
  // 排序逻辑已在计算属性中处理
};

// 处理搜索
const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
};

// 导航到文件夹
const navigateToFolder = (path) => {
  currentPath.value = path;
  // TODO: 重新加载该路径下的文件
  console.log('导航到文件夹:', path);
};

// 处理资料点击
const handleMaterialClick = (material) => {
  if (material.type === 'folder') {
    // 进入文件夹
    navigateToFolder(currentPath.value + material.name + '/');
    breadcrumbs.value.push({
      name: material.name,
      path: currentPath.value + material.name + '/'
    });
  } else {
    // 预览或下载文件
    previewMaterial(material);
  }
};

// 显示右键菜单
const showContextMenu = (material, event) => {
  contextMenu.value = {
    visible: true,
    x: event.clientX,
    y: event.clientY,
    material: material
  };
};

// 隐藏右键菜单
const hideContextMenu = () => {
  contextMenu.value.visible = false;
};

// 上传资料
const uploadMaterial = () => {
  console.log('上传资料');
  // TODO: 实现上传资料逻辑
};

// 创建文件夹
const createFolder = () => {
  console.log('创建文件夹');
  // TODO: 实现创建文件夹逻辑
};

// 下载资料
const downloadMaterial = (material) => {
  console.log('下载资料:', material);
  // TODO: 实现下载资料逻辑
};

// 分享资料
const shareMaterial = (material) => {
  console.log('分享资料:', material);
  // TODO: 实现分享资料逻辑
};

// 编辑资料
const editMaterial = (material) => {
  console.log('编辑资料:', material);
  // TODO: 实现编辑资料逻辑
};

// 重命名资料
const renameMaterial = (material) => {
  console.log('重命名资料:', material);
  // TODO: 实现重命名资料逻辑
};

// 删除资料
const deleteMaterial = (materialId) => {
  console.log('删除资料:', materialId);
  // TODO: 实现删除资料逻辑
};

// 预览资料
const previewMaterial = (material) => {
  console.log('预览资料:', material);
  // TODO: 实现预览资料逻辑
};

// 点击外部隐藏右键菜单
const handleClickOutside = (event) => {
  if (contextMenu.value.visible && !event.target.closest('.context-menu')) {
    hideContextMenu();
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadMaterials();
  document.addEventListener('click', handleClickOutside);
});

// 组件卸载时清理事件监听
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
/* 课程资料样式 */
.course-materials {
  background-color: var(--background-color, #ffffff);
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  background-color: var(--background-color, #ffffff);
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color, #1f2937);
  margin: 0;
}

.section-actions {
  display: flex;
  gap: 0.75rem;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.btn-primary {
  background-color: var(--primary-color, #6366f1);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-color-dark, #4f46e5);
}

.btn-secondary {
  background-color: var(--background-color-secondary, #f3f4f6);
  color: var(--text-color, #374151);
  border-color: var(--border-color, #d1d5db);
}

.btn-secondary:hover {
  background-color: var(--background-color-tertiary, #e5e7eb);
}

/* 资料统计 */
.materials-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  padding: 1.5rem;
  background-color: var(--background-color-secondary, #f9fafb);
  border-bottom: 1px solid var(--border-color, #e5e7eb);
}

.stat-card {
  text-align: center;
  padding: 1rem;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color, #6366f1);
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-color-secondary, #6b7280);
}

/* 文件操作栏 */
.file-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  background-color: white;
}

.view-controls {
  display: flex;
  gap: 0.5rem;
}

.view-btn {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--border-color, #d1d5db);
  border-radius: 0.375rem;
  background-color: white;
  color: var(--text-color-secondary, #6b7280);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
}

.view-btn:hover {
  background-color: var(--background-color-secondary, #f3f4f6);
}

.view-btn.active {
  background-color: var(--primary-color, #6366f1);
  color: white;
  border-color: var(--primary-color, #6366f1);
}

.file-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.sort-select {
  padding: 0.5rem;
  border: 1px solid var(--border-color, #d1d5db);
  border-radius: 0.375rem;
  font-size: 0.875rem;
  background-color: white;
}

.search-box {
  position: relative;
  width: 250px;
}

.search-input {
  width: 100%;
  padding: 0.5rem 2.5rem 0.5rem 1rem;
  border: 1px solid var(--border-color, #d1d5db);
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: border-color 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color, #6366f1);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.search-icon {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1rem;
  height: 1rem;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%236b7280'%3E%3Cpath fill-rule='evenodd' d='M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z' clip-rule='evenodd' /%3E%3C/svg%3E");
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

/* 面包屑导航 */
.breadcrumb {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  background-color: var(--background-color-secondary, #f9fafb);
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  font-size: 0.875rem;
}

.breadcrumb-item {
  color: var(--text-color-secondary, #6b7280);
  cursor: pointer;
  transition: color 0.2s;
}

.breadcrumb-item:hover {
  color: var(--primary-color, #6366f1);
}

.breadcrumb-item:last-child {
  color: var(--text-color, #1f2937);
  font-weight: 500;
}

.breadcrumb-separator {
  width: 0.75rem;
  height: 0.75rem;
  margin: 0 0.5rem;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%236b7280'%3E%3Cpath fill-rule='evenodd' d='M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z' clip-rule='evenodd' /%3E%3C/svg%3E");
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--text-color-secondary, #6b7280);
}

.loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 2px solid var(--border-color, #e5e7eb);
  border-top: 2px solid var(--primary-color, #6366f1);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 资料容器 */
.materials-container {
  padding: 1rem;
}

.materials-container.list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.materials-container.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

/* 资料项目 */
.material-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 0.5rem;
  background-color: white;
  cursor: pointer;
  transition: all 0.2s;
}

.material-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-color: var(--primary-color, #6366f1);
}

.materials-container.grid .material-item {
  flex-direction: column;
  text-align: center;
  aspect-ratio: 1;
}

.material-icon {
  flex-shrink: 0;
  width: 3rem;
  height: 3rem;
  margin-right: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.materials-container.grid .material-icon {
  margin-right: 0;
  margin-bottom: 1rem;
  width: 4rem;
  height: 4rem;
}

.material-info {
  flex: 1;
  min-width: 0;
}

.materials-container.grid .material-info {
  text-align: center;
}

.material-name {
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-color, #1f2937);
  margin: 0 0 0.5rem 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.materials-container.grid .material-name {
  white-space: normal;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.material-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
}

.materials-container.grid .material-meta {
  flex-direction: column;
  gap: 0.25rem;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: var(--text-color-secondary, #6b7280);
}

.material-description {
  font-size: 0.875rem;
  color: var(--text-color-secondary, #6b7280);
  margin: 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.material-actions {
  display: flex;
  gap: 0.5rem;
  margin-left: 1rem;
  flex-shrink: 0;
}

.materials-container.grid .material-actions {
  margin-left: 0;
  margin-top: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.375rem 0.75rem;
  border: 1px solid var(--border-color, #d1d5db);
  border-radius: 0.25rem;
  background-color: white;
  color: var(--text-color, #374151);
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s;
}

.action-btn:hover {
  background-color: var(--background-color-secondary, #f3f4f6);
}

.action-btn.danger {
  color: #dc2626;
  border-color: #fecaca;
}

.action-btn.danger:hover {
  background-color: #fef2f2;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.empty-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-color, #1f2937);
  margin: 0 0 0.5rem 0;
}

.empty-description {
  color: var(--text-color-secondary, #6b7280);
  margin: 0 0 1.5rem 0;
}

/* 右键菜单 */
.context-menu {
  position: fixed;
  background-color: white;
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 0.375rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 150px;
  overflow: hidden;
}

.context-menu-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  color: var(--text-color, #374151);
  cursor: pointer;
  transition: background-color 0.2s;
}

.context-menu-item:hover {
  background-color: var(--background-color-secondary, #f3f4f6);
}

.context-menu-item.danger {
  color: #dc2626;
}

.context-menu-item.danger:hover {
  background-color: #fef2f2;
}

.context-menu-divider {
  height: 1px;
  background-color: var(--border-color, #e5e7eb);
  margin: 0.25rem 0;
}

/* 文件图标 */
.btn-icon, .upload-icon, .folder-icon, .list-icon, .grid-icon, .size-icon, .date-icon,
.download-icon, .share-icon, .edit-icon, .delete-icon, .pdf-icon, .word-icon, .ppt-icon,
.excel-icon, .image-icon, .video-icon, .audio-icon, .file-icon {
  width: 1rem;
  height: 1rem;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

.material-icon .pdf-icon, .material-icon .word-icon, .material-icon .ppt-icon,
.material-icon .excel-icon, .material-icon .folder-icon, .material-icon .file-icon {
  width: 100%;
  height: 100%;
}

.upload-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

.folder-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%23f59e0b'%3E%3Cpath d='M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z' /%3E%3C/svg%3E");
}

.pdf-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%23dc2626'%3E%3Cpath fill-rule='evenodd' d='M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

.word-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%232563eb'%3E%3Cpath fill-rule='evenodd' d='M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

.ppt-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%23ea580c'%3E%3Cpath fill-rule='evenodd' d='M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

.excel-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%2316a34a'%3E%3Cpath fill-rule='evenodd' d='M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

.file-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%236b7280'%3E%3Cpath fill-rule='evenodd' d='M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

/* 其他图标 */
.list-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

.grid-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath d='M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z' /%3E%3C/svg%3E");
}

.size-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

.date-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

.download-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

.share-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath d='M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z' /%3E%3C/svg%3E");
}

.edit-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath d='M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z' /%3E%3C/svg%3E");
}

.delete-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

/* 响应式设计 */
@media (max-width: 768px) {
  .materials-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .file-toolbar {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .file-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .search-box {
    width: 100%;
  }

  .materials-container.grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }

  .material-item {
    flex-direction: column;
    text-align: center;
  }

  .material-icon {
    margin-right: 0;
    margin-bottom: 1rem;
  }

  .material-actions {
    margin-left: 0;
    margin-top: 1rem;
    justify-content: center;
    flex-wrap: wrap;
  }
}
</style>