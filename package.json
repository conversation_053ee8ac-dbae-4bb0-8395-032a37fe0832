{"name": "teacher-prep-assistant", "version": "1.0.0", "description": "教师备课助手应用", "main": "index.js", "scripts": {"dev": "vite", "start": "vite", "build": "vite build", "preview": "vite preview"}, "repository": {"type": "git", "url": "git+https://github.com/seeksdream/relation-graph-vue3-demo.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/seeksdream/relation-graph-vue3-demo/issues"}, "homepage": "https://github.com/seeksdream/relation-graph-vue3-demo#readme", "dependencies": {"@coze/api": "^1.3.5", "@upstash/context7-mcp": "^1.0.14", "@vitejs/plugin-vue": "^4.0.0", "axios": "^1.6.2", "docx": "^9.5.1", "element-plus": "^2.10.3", "js-cookie": "^3.0.5", "mcp-chain-of-draft-server": "^1.1.0", "mcp-cursor-companion": "^1.2.0", "mcp-sequential-thinking": "^0.6.7", "mcp-sequentialthinking-tools": "^0.0.2", "mcp-shrimp-task-manager": "^1.0.16", "mcp-think-tank": "^2.0.6", "motion-v": "^1.3.1", "pinia": "^2.1.7", "relation-graph-vue3": "^2.2.10", "task-master-ai": "^0.14.0", "taskqueue-mcp": "^1.4.1", "vite": "^4.1.2", "vue": "^3.2.47", "vue-router": "^4.2.5"}}