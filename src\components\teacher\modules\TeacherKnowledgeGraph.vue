<template>
  <div class="knowledge-graph-panel">
    <!-- 子标签导航 -->
    <div class="sub-tabs">
      <div 
        class="sub-tab-item" 
        :class="{ active: subActiveTab === 'outline' }" 
        @click="subActiveTab = 'outline'"
      >
        <i class="sub-tab-icon outline-icon"></i>
        <span>知识大纲</span>
      </div>
      <div 
        class="sub-tab-item" 
        :class="{ active: subActiveTab === 'graphview' }" 
        @click="subActiveTab = 'graphview'"
      >
        <i class="sub-tab-icon list-icon"></i>
        <span>图谱列表</span>
      </div>
    </div>
    
    <!-- 知识大纲内容 -->
    <div v-if="subActiveTab === 'outline'" class="sub-panel outline-content">
      <div v-if="selectedOutline" class="selected-outline-container">
        <div class="outline-header">
          <button class="back-button" @click="selectedOutline = null">
            <i class="back-icon"></i>
            <span>返回大纲列表</span>
          </button>
          <h3 class="outline-title">{{ selectedOutline.title }}</h3>
        </div>
        <iframe :src="`/outline?embedded=true&onlyOutline=true&courseId=${selectedOutline.id}`" class="embedded-view"></iframe>
      </div>
      <MyOutlineList v-else @select-outline="handleOutlineSelect" />
    </div>
    
    <!-- 知识图谱列表内容 -->
    <div v-if="subActiveTab === 'graphview'" class="sub-panel graphview-content">
      <div v-if="selectedGraph" class="selected-graph-container">
        <div class="graph-header">
          <button class="back-button" @click="selectedGraph = null">
            <i class="back-icon"></i>
            <span>返回图谱列表</span>
          </button>
          <h3 class="graph-title">{{ selectedGraph.title }}</h3>
        </div>
        <iframe :src="`/graph?embedded=true&courseId=${selectedGraph.id}`" class="embedded-view"></iframe>
      </div>
      <MyGraphList v-else @select-graph="handleGraphSelect" />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import MyOutlineList from '@/components/my/MyOutlineList.vue';
import MyGraphList from '@/components/my/MyGraphList.vue';

// 子标签页状态
const subActiveTab = ref('outline');

// 选中的大纲和图谱
const selectedOutline = ref(null);
const selectedGraph = ref(null);

// 处理大纲选择
const handleOutlineSelect = (outline) => {
  selectedOutline.value = outline;
};

// 处理图谱选择
const handleGraphSelect = (graph) => {
  selectedGraph.value = graph;
};
</script>

<style scoped>
/* 知识图谱面板样式 */
.knowledge-graph-panel {
  flex: 1;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 子标签导航样式 */
.sub-tabs {
  display: flex;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  padding: 0;
  margin-bottom: 1.5rem;
}

.sub-tab-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;
  font-weight: 500;
}

.sub-tab-item:hover {
  color: var(--primary-color);
  background-color: rgba(66, 153, 225, 0.05);
}

.sub-tab-item.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
  background-color: rgba(66, 153, 225, 0.1);
}

.sub-tab-icon {
  width: 16px;
  height: 16px;
  margin-right: 0.5rem;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0.7;
}

.sub-tab-item.active .sub-tab-icon {
  opacity: 1;
}

/* 子面板样式 */
.sub-panel {
  flex: 1;
  overflow: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(150, 150, 150, 0.3) transparent;
}

.sub-panel::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.sub-panel::-webkit-scrollbar-track {
  background: transparent;
}

.sub-panel::-webkit-scrollbar-thumb {
  background: rgba(150, 150, 150, 0.3);
  border-radius: 4px;
}

.sub-panel::-webkit-scrollbar-thumb:hover {
  background: rgba(150, 150, 150, 0.5);
}

/* 嵌入式视图 */
.embedded-view {
  width: 100%;
  height: 95vh;
  min-height: 500px;
  border: none;
  border-radius: var(--border-radius-md);
}

/* 大纲相关样式 */
.selected-outline-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.outline-header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  background-color: var(--background-color);
}

.back-button {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  color: #007AFF;
  font-size: 0.9375rem;
  font-weight: 500;
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-button:hover {
  background-color: rgba(0, 122, 255, 0.1);
}

.back-button .back-icon {
  margin-right: 0.375rem;
  width: 16px;
  height: 16px;
  position: relative;
}

.back-button .back-icon::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  width: 10px;
  height: 10px;
  border-left: 2px solid #007AFF;
  border-bottom: 2px solid #007AFF;
  transform: translateY(-50%) rotate(45deg);
}

.outline-title {
  margin: 0 0 0 1rem;
  font-size: 1.125rem;
  font-weight: 500;
}

/* 知识图谱列表相关样式 */
.selected-graph-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.graph-header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  background-color: var(--background-color);
}

.graph-title {
  margin: 0 0 0 1rem;
  font-size: 1.125rem;
  font-weight: 500;
}

/* 子标签图标 */
.outline-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M3 9h14V7H3v2zm0 4h14v-2H3v2zm0 4h14v-2H3v2zm16 0h2v-2h-2v2zm0-10v2h2V7h-2zm0 6h2v-2h-2v2z'/%3E%3C/svg%3E");
}

.list-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234299e1'%3E%3Cpath d='M3 13h2v-2H3v2zm0 4h2v-2H3v2zm0-8h2V7H3v2zm4 4h14v-2H7v2zm0 4h14v-2H7v2zM7 7v2h14V7H7z'/%3E%3C/svg%3E");
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
  .outline-header,
  .graph-header {
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }
}
</style>
