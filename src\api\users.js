import request from './axios';

/**
 * 用户管理相关API
 * 用于获取用户列表、学生列表等
 */

/**
 * 获取用户列表
 * @param {Object} params - 查询参数
 * @param {string} params.userName - 用户名
 * @param {string} params.nickName - 昵称
 * @param {string} params.email - 邮箱
 * @param {string} params.phonenumber - 手机号
 * @param {string} params.status - 状态
 * @param {number} params.deptId - 部门ID
 * @param {number} params.pageNum - 页码
 * @param {number} params.pageSize - 每页大小
 * @returns {Promise<Object>} 用户列表响应数据
 */
export const getUserList = (params = {}) => {
  console.log('正在获取用户列表...', params);
  
  // 构建查询参数，过滤掉空值
  const cleanParams = {};
  Object.keys(params).forEach(key => {
    if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
      cleanParams[key] = params[key];
    }
  });

  return request({
    url: '/system/user/list',
    method: 'get',
    params: cleanParams,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  }).then(response => {
    console.log('获取用户列表成功:', response);
    
    // 处理响应数据格式
    if (response && typeof response === 'object') {
      // 标准分页响应格式
      if (response.rows && Array.isArray(response.rows)) {
        return {
          total: response.total || 0,
          rows: response.rows,
          code: response.code || 0,
          msg: response.msg || '成功'
        };
      }
      // 如果响应只有数组数据
      else if (Array.isArray(response)) {
        return {
          total: response.length,
          rows: response,
          code: 0,
          msg: '成功'
        };
      }
      // 其他格式，尝试适配
      else {
        return {
          total: response.total || 0,
          rows: response.rows || response.data || [],
          code: response.code === 200 ? 0 : (response.code || 0),
          msg: response.msg || response.message || '成功'
        };
      }
    }

    // 如果响应格式不符合预期，返回空数据
    return {
      total: 0,
      rows: [],
      code: 0,
      msg: '成功'
    };
  }).catch(error => {
    console.error('获取用户列表失败:', error);
    // 返回错误格式，保持与TableDataInfo一致
    return {
      total: 0,
      rows: [],
      code: -1,
      msg: error.message || '获取用户列表失败'
    };
  });
};

/**
 * 根据部门ID获取用户列表（学生列表）
 * @param {number} deptId - 部门ID（班级ID）
 * @param {Object} additionalParams - 额外的查询参数
 * @returns {Promise<Object>} 用户列表响应数据
 */
export const getUserListByDept = (deptId, additionalParams = {}) => {
  console.log('正在根据部门ID获取用户列表...', deptId, additionalParams);
  
  if (!deptId) {
    console.error('获取用户列表失败: 部门ID不能为空');
    return Promise.resolve({
      total: 0,
      rows: [],
      code: -1,
      msg: '部门ID不能为空'
    });
  }

  const params = {
    deptId: deptId,
    ...additionalParams
  };
  
  return getUserList(params);
};

/**
 * 根据课程ID获取学生列表
 * 注意：这个API可能需要根据实际后端接口调整
 * @param {number} courseId - 课程ID
 * @param {Object} additionalParams - 额外的查询参数
 * @returns {Promise<Object>} 学生列表响应数据
 */
export const getStudentListByCourse = (courseId, additionalParams = {}) => {
  console.log('正在根据课程ID获取学生列表...', courseId, additionalParams);
  
  if (!courseId) {
    console.error('获取学生列表失败: 课程ID不能为空');
    return Promise.resolve({
      total: 0,
      rows: [],
      code: -1,
      msg: '课程ID不能为空'
    });
  }

  // 这里可能需要根据实际的后端API调整
  // 如果有专门的课程学生接口，使用专门的接口
  // 否则可以通过课程信息获取对应的班级ID，再获取学生列表
  const params = {
    courseId: courseId,
    ...additionalParams
  };
  
  return request({
    url: '/core/course/students',
    method: 'get',
    params: params,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  }).then(response => {
    console.log('获取课程学生列表成功:', response);
    
    // 处理响应数据格式
    if (response && typeof response === 'object') {
      // 标准分页响应格式
      if (response.rows && Array.isArray(response.rows)) {
        return {
          total: response.total || 0,
          rows: response.rows,
          code: response.code || 0,
          msg: response.msg || '成功'
        };
      }
      // 如果响应只有数组数据
      else if (Array.isArray(response)) {
        return {
          total: response.length,
          rows: response,
          code: 0,
          msg: '成功'
        };
      }
      // 其他格式，尝试适配
      else {
        return {
          total: response.total || 0,
          rows: response.rows || response.data || [],
          code: response.code === 200 ? 0 : (response.code || 0),
          msg: response.msg || response.message || '成功'
        };
      }
    }

    // 如果响应格式不符合预期，返回空数据
    return {
      total: 0,
      rows: [],
      code: 0,
      msg: '成功'
    };
  }).catch(error => {
    console.error('获取课程学生列表失败:', error);
    
    // 如果专门的课程学生接口不存在，尝试通过其他方式获取
    console.warn('专门的课程学生接口可能不存在，尝试其他方式...');
    
    // 返回错误格式，保持与TableDataInfo一致
    return {
      total: 0,
      rows: [],
      code: -1,
      msg: error.message || '获取课程学生列表失败'
    };
  });
};

/**
 * 搜索用户
 * @param {Object} searchParams - 搜索参数
 * @param {string} searchParams.keyword - 搜索关键词
 * @param {number} searchParams.deptId - 部门ID
 * @param {number} searchParams.pageNum - 页码
 * @param {number} searchParams.pageSize - 每页大小
 * @returns {Promise<Object>} 搜索结果
 */
export const searchUsers = (searchParams) => {
  console.log('正在搜索用户...', searchParams);
  
  // 将关键词映射到具体的搜索字段
  const params = { ...searchParams };
  if (searchParams.keyword) {
    // 可以同时搜索用户名和昵称
    params.userName = searchParams.keyword;
    params.nickName = searchParams.keyword;
  }
  
  return getUserList(params);
};

/**
 * 获取用户详情
 * @param {number} userId - 用户ID
 * @returns {Promise<Object>} 用户详情
 */
export const getUserDetail = (userId) => {
  console.log('正在获取用户详情...', userId);
  
  if (!userId) {
    console.error('获取用户详情失败: 用户ID不能为空');
    return Promise.reject(new Error('用户ID不能为空'));
  }
  
  return request({
    url: `/system/user/${userId}`,
    method: 'get'
  }).then(response => {
    console.log('获取用户详情成功:', response);
    return response;
  }).catch(error => {
    console.error('获取用户详情失败:', error);
    throw error;
  });
};
