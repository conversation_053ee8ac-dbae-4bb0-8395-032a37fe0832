<template>
  <div class="course-discussion-demo">
    <div class="demo-header">
      <h1>教师端课程讨论模块演示</h1>
      <p>展示会话列表API的集成效果</p>
    </div>

    <div class="demo-content">
      <!-- 课程信息卡片 -->
      <div class="course-info-card">
        <h2>课程信息</h2>
        <div class="course-details">
          <div class="detail-item">
            <label>课程ID:</label>
            <input v-model="demoData.courseId" type="number" @change="handleCourseIdChange" />
          </div>
          <div class="detail-item">
            <label>课程名称:</label>
            <span>{{ demoData.courseName }}</span>
          </div>
          <div class="detail-item">
            <label>学期:</label>
            <span>{{ demoData.semester }}</span>
          </div>
        </div>
      </div>

      <!-- 讨论模块 -->
      <div class="discussion-module">
        <CourseDiscussion :course-id="demoData.courseId" :key="demoData.courseId" />
      </div>
    </div>

    <!-- 说明文档 -->
    <div class="documentation">
      <h2>功能说明</h2>
      <div class="doc-section">
        <h3>已实现的功能</h3>
        <ul>
          <li>✅ 会话列表查询 - 调用 <code>/core/session/list</code> 接口</li>
          <li>✅ 创建新会话 - 调用 <code>/core/session</code> POST 接口</li>
          <li>✅ 删除会话 - 调用 <code>/core/session/{id}</code> DELETE 接口</li>
          <li>✅ 响应式设计 - 适配移动端和桌面端</li>
          <li>✅ 错误处理 - 网络错误和API错误的处理</li>
          <li>✅ 加载状态 - 显示加载动画和状态</li>
        </ul>
      </div>

      <div class="doc-section">
        <h3>API接口规范</h3>
        <div class="api-spec">
          <h4>查询会话列表</h4>
          <div class="api-details">
            <div><strong>接口地址:</strong> <code>/core/session/list</code></div>
            <div><strong>请求方式:</strong> <code>GET</code></div>
            <div><strong>响应格式:</strong></div>
            <pre class="response-example">{{responseExample}}</pre>
          </div>
        </div>
      </div>

      <div class="doc-section">
        <h3>技术实现</h3>
        <ul>
          <li><strong>前端框架:</strong> Vue 3 + Composition API</li>
          <li><strong>HTTP客户端:</strong> Axios (通过 request 封装)</li>
          <li><strong>状态管理:</strong> Vue 3 Reactive API</li>
          <li><strong>样式:</strong> CSS3 + 响应式设计</li>
          <li><strong>错误处理:</strong> Try-catch + 用户友好提示</li>
        </ul>
      </div>

      <div class="doc-section">
        <h3>使用说明</h3>
        <ol>
          <li>修改上方的课程ID来测试不同课程的会话列表</li>
          <li>点击"新建会话"按钮创建测试会话</li>
          <li>点击会话项右侧的"删除"按钮删除会话</li>
          <li>观察浏览器控制台查看API调用日志</li>
          <li>检查网络面板查看实际的HTTP请求</li>
        </ol>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import CourseDiscussion from '@/components/teacher/modules/TeacherCourseDetail/CourseDiscussion.vue';

// 演示数据
const demoData = reactive({
  courseId: 1,
  courseName: '结构力学',
  semester: '2024春季学期'
});

// API响应示例
const responseExample = ref(`{
  "total": 2,
  "rows": [
    {
      "createBy": "1",
      "createTime": "2025-07-28 10:41:30",
      "updateBy": null,
      "updateTime": "2025-07-28 10:41:29",
      "remark": null,
      "id": 9,
      "name": "汤臣一品业主群"
    },
    {
      "createBy": "2",
      "createTime": "2025-07-31 16:49:57",
      "updateBy": null,
      "updateTime": "2025-07-31 16:49:58",
      "remark": null,
      "id": 11,
      "name": null
    }
  ],
  "code": 200,
  "msg": "查询成功"
}`);

// 处理课程ID变化
const handleCourseIdChange = () => {
  console.log('课程ID已更改为:', demoData.courseId);
  // 更新课程名称（模拟数据）
  const courseNames = {
    1: '结构力学',
    2: '材料力学', 
    3: '理论力学',
    4: '流体力学'
  };
  demoData.courseName = courseNames[demoData.courseId] || `课程 ${demoData.courseId}`;
};
</script>

<style scoped>
.course-discussion-demo {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 2rem;
}

.demo-header {
  text-align: center;
  margin-bottom: 2rem;
}

.demo-header h1 {
  color: #333;
  margin-bottom: 0.5rem;
}

.demo-header p {
  color: #666;
  font-size: 1.1rem;
}

.demo-content {
  max-width: 1200px;
  margin: 0 auto;
}

.course-info-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.course-info-card h2 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: #333;
}

.course-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.detail-item label {
  font-weight: 600;
  color: #555;
  min-width: 80px;
}

.detail-item input {
  padding: 0.25rem 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 100px;
}

.discussion-module {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 2rem;
}

.documentation {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.documentation h2 {
  margin-top: 0;
  color: #333;
  border-bottom: 2px solid #007bff;
  padding-bottom: 0.5rem;
}

.doc-section {
  margin-bottom: 2rem;
}

.doc-section h3 {
  color: #555;
  margin-bottom: 1rem;
}

.doc-section ul, .doc-section ol {
  line-height: 1.6;
}

.doc-section li {
  margin-bottom: 0.5rem;
}

.api-spec {
  background: #f8f9fa;
  border-radius: 4px;
  padding: 1rem;
  margin-top: 1rem;
}

.api-spec h4 {
  margin-top: 0;
  color: #333;
}

.api-details div {
  margin-bottom: 0.5rem;
}

.response-example {
  background: #2d3748;
  color: #e2e8f0;
  padding: 1rem;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 0.875rem;
  line-height: 1.4;
  margin-top: 0.5rem;
}

code {
  background: #e2e8f0;
  color: #2d3748;
  padding: 0.125rem 0.25rem;
  border-radius: 3px;
  font-size: 0.875rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .course-discussion-demo {
    padding: 1rem;
  }
  
  .course-details {
    grid-template-columns: 1fr;
  }
  
  .detail-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .detail-item label {
    min-width: auto;
  }
}
</style>
