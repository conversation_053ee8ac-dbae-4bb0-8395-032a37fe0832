<template>
  <div v-if="visible" class="homework-dialog-overlay" @click="handleOverlayClick">
    <div class="homework-dialog" @click.stop>
      <div class="dialog-header">
        <h3 class="dialog-title">{{ isEdit ? '编辑作业' : '布置作业' }}</h3>
        <button class="close-btn" @click="closeDialog">
          <i class="close-icon">×</i>
        </button>
      </div>
      
      <div class="dialog-content">
        <form @submit.prevent="handleSubmit">
          <div class="form-group">
            <label for="homework-name" class="form-label">作业名称 *</label>
            <input
              id="homework-name"
              v-model="formData.name"
              type="text"
              class="form-input"
              placeholder="请输入作业名称"
              required
            />
          </div>
          
          <div class="form-group">
            <label for="homework-content" class="form-label">作业内容</label>
            <textarea
              id="homework-content"
              v-model="formData.gettContent"
              class="form-textarea"
              placeholder="请输入作业内容和要求"
              rows="4"
            ></textarea>
          </div>
          
          <div class="form-group">
            <label for="homework-file" class="form-label">附件文件</label>
            <input
              id="homework-file"
              v-model="formData.gettFile"
              type="text"
              class="form-input"
              placeholder="文件路径或URL"
            />
          </div>
          
          <div class="form-group">
            <label for="homework-remark" class="form-label">备注</label>
            <textarea
              id="homework-remark"
              v-model="formData.remark"
              class="form-textarea"
              placeholder="备注信息"
              rows="2"
            ></textarea>
          </div>
        </form>
      </div>
      
      <div class="dialog-footer">
        <button type="button" class="btn btn-secondary" @click="closeDialog">
          取消
        </button>
        <button 
          type="button" 
          class="btn btn-primary" 
          @click="handleSubmit"
          :disabled="!formData.name || loading"
        >
          <span v-if="loading">{{ isEdit ? '更新中...' : '创建中...' }}</span>
          <span v-else>{{ isEdit ? '更新作业' : '创建作业' }}</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue';

// 定义props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  homework: {
    type: Object,
    default: null
  },
  courseId: {
    type: [String, Number],
    required: true
  }
});

// 定义emits
const emit = defineEmits(['close', 'submit']);

// 响应式数据
const loading = ref(false);
const formData = ref({
  name: '',
  gettContent: '',
  gettFile: '',
  remark: '',
  courseId: props.courseId
});

// 计算属性
const isEdit = computed(() => !!props.homework);

// 重置表单
const resetForm = () => {
  formData.value = {
    name: '',
    gettContent: '',
    gettFile: '',
    remark: '',
    courseId: props.courseId
  };
};

// 监听homework变化，初始化表单数据
watch(() => props.homework, (newHomework) => {
  if (newHomework) {
    // 编辑模式，填充现有数据
    formData.value = {
      id: newHomework.originalData?.id || newHomework.id,
      name: newHomework.originalData?.name || newHomework.title || '',
      gettContent: newHomework.originalData?.gettContent || newHomework.description || '',
      gettFile: newHomework.originalData?.gettFile || '',
      remark: newHomework.originalData?.remark || '',
      courseId: props.courseId
    };
  } else {
    // 新建模式，重置表单
    resetForm();
  }
}, { immediate: true });

// 监听courseId变化
watch(() => props.courseId, (newCourseId) => {
  formData.value.courseId = newCourseId;
});

// 关闭对话框
const closeDialog = () => {
  resetForm();
  emit('close');
};

// 处理遮罩层点击
const handleOverlayClick = () => {
  closeDialog();
};

// 提交表单
const handleSubmit = async () => {
  if (!formData.value.name) {
    alert('请输入作业名称');
    return;
  }
  
  loading.value = true;
  try {
    // 发出提交事件，由父组件处理具体的API调用
    emit('submit', { ...formData.value });
  } catch (error) {
    console.error('提交作业失败:', error);
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
/* 对话框遮罩层 */
.homework-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* 对话框主体 */
.homework-dialog {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 对话框头部 */
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.dialog-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6b7280;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: all 0.2s;
}

.close-btn:hover {
  background-color: #e5e7eb;
  color: #374151;
}

/* 对话框内容 */
.dialog-content {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: border-color 0.2s;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

/* 对话框底部 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-secondary {
  background-color: #f3f4f6;
  color: #374151;
}

.btn-secondary:hover {
  background-color: #e5e7eb;
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #2563eb;
}

.btn-primary:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .homework-dialog {
    width: 95%;
    margin: 1rem;
  }
  
  .dialog-header,
  .dialog-content,
  .dialog-footer {
    padding: 1rem;
  }
}
</style>
