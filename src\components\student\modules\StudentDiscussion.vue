<template>
  <div class="discussion-panel">
    <div class="discussion-list">
      <div
        v-for="discussion in discussions"
        :key="discussion.id"
        class="discussion-item"
        @click="viewDiscussion(discussion.id)"
      >
        <div class="discussion-header">
          <h3 class="discussion-title">{{ discussion.title }}</h3>
          <div class="discussion-course">{{ discussion.courseName }}</div>
        </div>
        <div class="discussion-info">
          <div class="info-item">
            <span class="info-label">发起人:</span>
            <span class="info-value">{{ discussion.author }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">回复数:</span>
            <span class="info-value">{{ discussion.replyCount }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">最后回复:</span>
            <span class="info-value">{{ discussion.lastReply }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

// 讨论数据
const discussions = ref([
  {
    id: 1,
    title: '关于结构力学学习方法的讨论',
    courseName: '结构力学',
    author: '张同学',
    replyCount: 15,
    lastReply: '2小时前'
  },
  {
    id: 2,
    title: '有限元分析软件使用心得',
    courseName: '有限元分析',
    author: '李同学',
    replyCount: 8,
    lastReply: '1天前'
  }
]);

// 讨论相关方法
const viewDiscussion = (discussionId) => {
  console.log('查看讨论:', discussionId);
  // TODO: 跳转到讨论详情页
};
</script>

<style scoped>
/* 讨论面板样式 */
.discussion-panel {
  flex: 1;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 讨论列表样式 */
.discussion-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  overflow-y: auto;
}

.discussion-item {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.discussion-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.discussion-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.discussion-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  flex: 1;
}

.discussion-course {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  background: #f3f4f6;
  color: #6b7280;
  margin-left: 1rem;
}

.discussion-info {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.info-label {
  font-size: 0.875rem;
  color: #6b7280;
}

.info-value {
  font-size: 0.875rem;
  font-weight: 500;
  color: #1f2937;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .discussion-info {
    flex-direction: column;
    gap: 0.5rem;
  }
}
</style>
