<template>
  <div class="homework-panel">
    <!-- 作业筛选标签 -->
    <div class="filter-tabs">
      <div 
        class="filter-tab" 
        :class="{ active: homeworkFilter === 'all' }" 
        @click="homeworkFilter = 'all'"
      >
        全部作业
      </div>
      <div 
        class="filter-tab" 
        :class="{ active: homeworkFilter === 'pending' }" 
        @click="homeworkFilter = 'pending'"
      >
        待完成
      </div>
      <div 
        class="filter-tab" 
        :class="{ active: homeworkFilter === 'submitted' }" 
        @click="homeworkFilter = 'submitted'"
      >
        已提交
      </div>
      <div 
        class="filter-tab" 
        :class="{ active: homeworkFilter === 'graded' }" 
        @click="homeworkFilter = 'graded'"
      >
        已批改
      </div>
    </div>
    
    <!-- 作业列表 -->
    <div v-if="filteredHomework.length > 0" class="homework-list">
      <div 
        v-for="homework in filteredHomework" 
        :key="homework.id"
        class="homework-item"
        @click="viewHomework(homework.id)"
      >
        <div class="homework-header">
          <h3 class="homework-title">{{ homework.title }}</h3>
          <div class="homework-status" :class="homework.status">
            {{ getHomeworkStatusText(homework.status) }}
          </div>
        </div>
        <div class="homework-info">
          <div class="info-item">
            <span class="info-label">课程:</span>
            <span class="info-value">{{ homework.courseName }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">截止时间:</span>
            <span class="info-value">{{ homework.deadline }}</span>
          </div>
          <div class="info-item" v-if="homework.score">
            <span class="info-label">成绩:</span>
            <span class="info-value">{{ homework.score }}分</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 空状态 -->
    <div v-else class="empty-state">
      <div class="empty-icon">📝</div>
      <h3 class="empty-title">暂无作业</h3>
      <p class="empty-description">当前没有符合条件的作业</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

// 作业筛选状态
const homeworkFilter = ref('pending');

// 学生作业数据
const homework = ref([
  {
    id: 1,
    title: '土木工程基础知识测试',
    courseName: '土木工程概论',
    deadline: '2024-12-15',
    status: 'pending',
    score: null
  },
  {
    id: 2,
    title: '有限元分析实验报告',
    courseName: '有限元分析',
    deadline: '2024-12-10',
    status: 'submitted',
    score: null
  },
  {
    id: 3,
    title: '结构力学计算题',
    courseName: '结构力学',
    deadline: '2024-11-30',
    status: 'graded',
    score: 85
  }
]);

// 筛选后的作业
const filteredHomework = computed(() => {
  if (homeworkFilter.value === 'all') {
    return homework.value;
  }
  return homework.value.filter(hw => hw.status === homeworkFilter.value);
});

// 作业相关方法
const viewHomework = (homeworkId) => {
  console.log('查看作业:', homeworkId);
  // TODO: 跳转到作业详情页
};

const getHomeworkStatusText = (status) => {
  const statusMap = {
    'pending': '待完成',
    'submitted': '已提交',
    'graded': '已批改'
  };
  return statusMap[status] || status;
};
</script>

<style scoped>
/* 作业面板样式 */
.homework-panel {
  flex: 1;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 筛选标签样式 */
.filter-tabs {
  display: flex;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  padding: 0;
  margin-bottom: 1.5rem;
}

.filter-tab {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;
  font-weight: 500;
}

.filter-tab:hover {
  color: var(--primary-color);
  background-color: rgba(66, 153, 225, 0.05);
}

.filter-tab.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
  background-color: rgba(66, 153, 225, 0.1);
}

/* 作业列表样式 */
.homework-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  overflow-y: auto;
}

.homework-item {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.homework-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.homework-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.homework-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  flex: 1;
}

.homework-status {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  margin-left: 1rem;
}

.homework-status.pending {
  background: #fef3c7;
  color: #d97706;
}

.homework-status.submitted {
  background: #dbeafe;
  color: #2563eb;
}

.homework-status.graded {
  background: #d1fae5;
  color: #059669;
}

.homework-info {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.info-label {
  font-size: 0.875rem;
  color: #6b7280;
}

.info-value {
  font-size: 0.875rem;
  font-weight: 500;
  color: #1f2937;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  margin-top: 1rem;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  opacity: 0.6;
}

.empty-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 1rem 0;
}

.empty-description {
  font-size: 1rem;
  color: var(--text-color-secondary);
  margin: 0 0 2rem 0;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .homework-info {
    flex-direction: column;
    gap: 0.5rem;
  }
}
</style>
